package com.sgmw.common.utils.network
import androidx.annotation.RequiresPermission
import com.sgmw.common.utils.Log


/**
 * Network state client
 *
 * @constructor Create empty Network state client
 */
object NetworkStateClient {

    private val TAG = NetworkStateClient::class.simpleName

    private val mNetworkCallback = NetworkCallbackImpl()

    /**
     * 注册网络监听客户端
     * @return Unit
     */
    @RequiresPermission(android.Manifest.permission.ACCESS_NETWORK_STATE)
    fun register() {
        // 仅做初始化使用
        Log.i(TAG, "register")
    }

    /**
     * 设置监听器
     * @param listener NetworkStateChangeListener 监听器
     * @return Unit
     */
    fun addListener(listener: NetworkStateChangeListener) {
        mNetworkCallback.addNetworkListener(listener)
    }

    /**
     * 移除监听器
     * @return Unit
     */
    fun removeListener(listener: NetworkStateChangeListener) {
        mNetworkCallback.removeListener(listener)
    }

    /**
     * 获取网络类型
     * 当前网络类型是缓存的最近一次连接的网络类型，当无网络连接时其实拿到的是上一次的
     * 所以网络是否连接应该作为第一判断，确定网络是连接状态时再获取当前的网络类型，因为网络类型中没有设定无网
     * @return NetworkTypeEnum 参照[NetworkTypeEnum]
     */
    fun getNetworkType(): NetworkTypeEnum = mNetworkCallback.currentNetworkType

    /**
     * 网络是否连接（效率高，读内存值）
     */
    fun isConnected(): Boolean {
        Log.d(TAG, "isConnected: ${mNetworkCallback.isConnected.get()}")
        return mNetworkCallback.isConnected.get()
    }

    /**
     * 实时检查网络的可用性
     */
    fun isRealNetworkConnected(): Boolean {
        return mNetworkCallback.isNetworkAvailable()
    }

}