package com.sgmw.common.utils;

import java.io.File;
import java.io.RandomAccessFile;


/**
 * 多资源模块日志管理
 *
 * <AUTHOR>
 */
public class Log {
    private static final String TAG = "KSongs";
    private static final String LOG_NAME = Log.class.getSimpleName() + ".java";
    private static File file;
    private static RandomAccessFile raf;
    private  static boolean sIsLoggable = true;
    private static boolean sIsLogWrite;

    private static final Object lock = new Object();

    static {
        //设置系统全局异常
        Thread.setDefaultUncaughtExceptionHandler((t, e) -> {
           Log.e("Exception",e);
        });
    }

    private Log() {
    }


    public static void setIsLoggable(boolean isLoggable){
        sIsLoggable = isLoggable;
    }

    public boolean isLoggable(){
        return sIsLoggable;
    }
    public void setIsLogWrite(boolean isLogWrite){
        sIsLogWrite = isLogWrite;
    }

    public static boolean isLogWrite(){
        return sIsLogWrite;
    }

    /**
     * 将字符串写入到文本文件中
     */
    private static void writeTxtToFile(String msg) {
        //生成文件夹之后，再生成文件，不然会出错
        // 每次写入时，都换行写
        if (!isLogWrite()){return;}
//
//        ThreadUtil.execute(() -> {
//            synchronized (lock) {
//                if (file == null || raf == null) {
//                    String date = new SimpleDateFormat("yyyyMMdd", Locale.getDefault()).format(Calendar.getInstance().getTime());
//                    String processName = null;
//                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
//                        processName = Application.getProcessName().replaceAll("/:", "_");
//                    }
//                    //每次启动使用新的 日志index命名，避免单个文件内容过多
//                    String key = "Log" + date + processName;
//                    int index = SpUtil.getLogIndex(key);
//                    index = index + 1;
//                    SpUtil.putLogInt(key, index);
//                    //保持日志数据不过多，避免占用过多车机存储空间
//                    String logPath = GlobalUtil.getContext().getExternalFilesDir("") + "/Log";
//                    File logPathFile = new File(logPath);
//                    if (logPathFile.exists()) {
//                        if (logPathFile.isDirectory()) {
//                            String[] files = logPathFile.list();
//                            if (files != null && files.length > 20) {
//                                for (String name : files) {
//                                    if (name.startsWith("AccountLog(" + processName + ")")) {
//                                        File file1 = new File(logPath + "/" + name);
//                                        file1.delete();
//                                    }
//                                }
//                            }
//                        }
//                    }
//                    //
//                    String strFilePath = logPath + "/AccountLog(" + processName + ")(" + date + ")_" + index + ".txt";
//                    try {
//                        file = new File(strFilePath);
//                        if (!file.exists()) {
//                            android.util.Log.d(TAG, "Create the file:" + strFilePath);
//                            file.getParentFile().mkdirs();
//                            file.createNewFile();
//                        }
//                        raf = new RandomAccessFile(file, "rwd");
//                    } catch (Exception e) {
//                        android.util.Log.e(TAG, "Error on write File:" + e);
//                    }
//                }
//
//
//                String strContent = msg + "\r\n";
//                try {
//                    raf.seek(file.length());
//                    raf.write(strContent.getBytes());
//                } catch (IOException e) {
//                    android.util.Log.e(TAG, "writeTxtToFile", e);
//                }
//            }
//        });
    }

    /**
     * 获取当前日志打印位置
     */
    private static String addStackTrace(String msg) {
        StackTraceElement[] elements = Thread.currentThread().getStackTrace();
        for (int index = 5; index >= 0; index--) {
            if (elements.length <= index) {
                continue;
            }
            if (LOG_NAME.equals(elements[index].getFileName())) {
                StackTraceElement element = elements[index + 1];
                return "(" + element.getFileName() + ":" + element.getLineNumber() + ")"  + msg;
            }
        }
        return msg;
    }

    public static void v(String msg) {
        v(TAG, msg,null);
    }


    public static void v(String msg, Throwable t) {
       v(TAG, msg,t);
    }

    public static void v(String tag, String msg) {
        v(tag, msg,null);
    }
    public static void v(String tag, String msg,Throwable t) {
        msg = addStackTrace(msg);
        android.util.Log.v(tag, msg,t);
        writeTxtToFile(tag + ": " + msg + "" + getStackTraceString(t));
    }

    public static void d(String msg) {
       d(TAG, msg,null);
    }

    public static void d(String msg, Throwable t) {
        d(TAG, msg,t);
    }

    public static void d(String tag, String msg) {
        d(tag, msg,null);
    }

    public static void d(String tag, String msg,Throwable t) {
        msg = addStackTrace(msg);
        android.util.Log.d(tag, msg,t);
        writeTxtToFile(tag + ": " + msg + "" + getStackTraceString(t));
    }
    public static void i(String msg) {
        i(TAG, msg,null);
    }

    public static void i(String msg, Throwable t) {
        i(TAG, msg,t);
    }
    public static void i(String tag, String msg) {
        i(tag, msg,null);
    }
    public static void i(String tag, String msg,Throwable t) {
        msg = addStackTrace(msg);
        android.util.Log.i(tag, msg,t);
        writeTxtToFile(tag + ": " + msg + "" + getStackTraceString(t));
    }
    public static void w(String msg) {
      w(TAG, msg,null);
    }

    public static void w(String msg, Throwable t) {
        w(TAG, msg,t);
    }

    public static void w(String tag, String msg) {
        w(tag, msg,null);
    }
    public static void w(String tag, String msg,Throwable t) {
        msg = addStackTrace(msg);
        android.util.Log.w(tag, msg,t);
        writeTxtToFile(tag + ": " + msg + "" + getStackTraceString(t));
    }
    public static void e(String msg) {
      e(TAG, msg,null);
    }

    public static void e(String msg, Throwable t) {
        e(TAG, msg,t);
    }

    public static void e(String tag,String msg) {
        e(tag, msg,null);
    }
    public static void e(String tag, String msg,Throwable t) {
        msg = addStackTrace(msg);
        android.util.Log.e(tag, msg,t);
        writeTxtToFile(tag + ": " + msg + "" + getStackTraceString(t));
    }

    public static String getStackTraceString(Throwable tr) {
        return android.util.Log.getStackTraceString(tr);
    }

    /**
     * 打印堆栈信息的方法
     */
    public static void printStackTrace(String msg) {
        String log = getStackTraceString(new Throwable(msg));
        log = log.replace("java.lang.Throwable:", "");
        android.util.Log.v(TAG, log);
        writeTxtToFile(TAG + ": " + log);
    }
}
