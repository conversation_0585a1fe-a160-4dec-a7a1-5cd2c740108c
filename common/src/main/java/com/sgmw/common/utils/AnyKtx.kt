package com.sgmw.common.utils

import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


/**
 * 检查内存压力状态的简单实现
 * 避免对app模块的依赖
 */
private fun isMemoryPressureHigh(): Bo<PERSON>an {
    return try {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()

        // 如果内存使用率超过75%，认为处于内存压力状态
        memoryUsageRatio >= 0.75f
    } catch (e: Exception) {
        // 如果检查内存状态时出现异常，保守地认为处于内存压力状态
        true
    }
}

/**
 * 全局协程异常处理器
 * 统一处理所有协程中的未捕获异常，防止crash传播到系统级处理器
 */
private val GlobalCoroutineExceptionHandler = CoroutineExceptionHandler { _, throwable ->
    try {
        // 检查内存压力，避免在内存不足时进行复杂操作
        if (isMemoryPressureHigh()) {
            // 内存压力高时，只记录简单日志
            Log.e("GlobalCoroutineExceptionHandler", "Coroutine exception under memory pressure: ${throwable.javaClass.simpleName}")
        } else {
            // 正常情况下记录详细异常信息
            Log.e("GlobalCoroutineExceptionHandler", "Uncaught coroutine exception", throwable)
        }
    } catch (e: Exception) {
        // 异常处理器本身出现异常时的最后防线
        Log.e("GlobalCoroutineExceptionHandler", "Exception in exception handler: $e, original: $throwable")
    }
}

/**
 * 主线程的协程 - 带异常处理器
 *
 * @param callback
 */
fun mainLaunch(callback: suspend () -> Unit) {
    CoroutineScope(Dispatchers.Main + GlobalCoroutineExceptionHandler).launch {
        try {
            callback.invoke()
        } catch (e: Exception) {
            // 双重保护：try-catch + 异常处理器
            Log.e("mainLaunch", "Exception in main coroutine", e)
        }
    }
}

/**
 * IO线程的协程 - 使用守护线程避免阻塞应用关闭，带异常处理器
 *
 * @param callback
 */
fun ioLaunch(callback: suspend () -> Unit) {
    CoroutineScope(DaemonThreadDispatcher.IO + GlobalCoroutineExceptionHandler).launch {
        try {
            callback.invoke()
        } catch (e: Exception) {
            // 双重保护：try-catch + 异常处理器
            Log.e("ioLaunch", "Exception in IO coroutine", e)
        }
    }
}


// 使用守护线程的单线程调度器，避免阻塞应用关闭
private val singleThreadDispatcher = DaemonThreadDispatcher.Single

/**
 * IO单线程，排队执行 - 使用守护线程避免阻塞应用关闭，带异常处理器
 */
fun ioSingleLaunch(callback: suspend () -> Unit) {
    CoroutineScope(singleThreadDispatcher + GlobalCoroutineExceptionHandler).launch {
        try {
            callback.invoke()
        } catch (e: Exception) {
            // 双重保护：try-catch + 异常处理器
            Log.e("ioSingleLaunch", "Exception in IO single coroutine", e)
        }
    }
}

/**
 * default线程的协程 - 使用守护线程避免阻塞应用关闭，带异常处理器
 *
 * @param callback
 */
fun defaultLaunch(callback: suspend () -> Unit) {
    CoroutineScope(DaemonThreadDispatcher.Default + GlobalCoroutineExceptionHandler).launch {
        try {
            callback.invoke()
        } catch (e: Exception) {
            // 双重保护：try-catch + 异常处理器
            Log.e("defaultLaunch", "Exception in default coroutine", e)
        }
    }
}