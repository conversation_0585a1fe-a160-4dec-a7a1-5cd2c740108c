package com.sgmw.common.mvvm.vm
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.sgmw.common.utils.StateLayoutEnum
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


/**
 * Base view model
 *
 * @constructor Create empty Base view model
 */
abstract class BaseViewModel : ViewModel() {

    /**
     * 控制状态视图的LiveData
     */
    val stateViewLD = MutableLiveData<StateLayoutEnum>()

    /**
     * 更改状态视图的状态
     *
     * @param hide Boolean 是否进行隐藏状态视图
     * @param loading Boolean 是否显示加载中视图
     * @param error Boolean 是否显示错误视图
     * @param noData Boolean 是否显示没有数据视图
     * @param withDelay Boolean error和noNetwork的时候，是否延迟1s再更新状态，默认为true有延迟
     * @return Unit
     * @throws IllegalArgumentException 如果入参没有传入任何参数或者为true的参数 >1 时，会抛出[IllegalArgumentException]
     *
     * postValue 存在一个问题 短时间内post两次信号 只会发送最后一条
     */
    @Throws(IllegalArgumentException::class)
    protected fun changeStateView(
        hide: Boolean = false,
        loading: Boolean = false,
        error: Boolean = false,
        noData: Boolean = false,
        noNetwork: Boolean = false,
        noLogin: Boolean = false,
        withDelay: Boolean = true
    ) {
        // 对参数进行校验
        var count = 0
        if (hide) count++
        if (loading) count++
        if (error) count++
        if (noData) count++
        if (noNetwork) count++
        if (noLogin) count++

        when {
            count == 0 -> throw IllegalArgumentException("必须设置一个参数为true")
            count > 1 -> throw IllegalArgumentException("只能有一个参数为true")
        }

        // 修改状态
        when {
            hide -> stateViewLD.postValue(StateLayoutEnum.SUCCESS)
            loading -> stateViewLD.postValue(StateLayoutEnum.LOADING)
            error -> updateViewStateWithDelay(StateLayoutEnum.ERROR, withDelay)
            noData -> stateViewLD.postValue(StateLayoutEnum.NO_DATA)
            noNetwork -> updateViewStateWithDelay(StateLayoutEnum.NO_NETWORK, withDelay)
            noLogin -> stateViewLD.postValue(StateLayoutEnum.NO_LOGIN)
        }
    }

    /**
     * 针对无网络时，页面切换请求数据，onError收到回调过快，导致闪现骨架图问题。
     * 延迟更新状态，优化视觉体验（产品已确认优化效果ok）
     */
    private fun updateViewStateWithDelay(viewState: StateLayoutEnum, withDelay: Boolean = false) {
        CoroutineScope(Dispatchers.Main).launch {
            if (withDelay) {
                withContext(Dispatchers.IO) {
                    delay(1000L)
                }
            }
            withContext(Dispatchers.Main) {
            stateViewLD.postValue(viewState)
            }
        }
    }


    /**
     * 同步方式设置展示view,避免上面异步方式导致的viewmodel.xxx设置值后需要设置view显隐性,post方式最后生效,导致显隐性异常
     */
    @Throws(IllegalArgumentException::class)
    protected fun changeStateViewV2(
        hide: Boolean = false,
        loading: Boolean = false,
        error: Boolean = false,
        noData: Boolean = false,
        noNetwork: Boolean = false,
        noLogin :Boolean = false
    ) {
        // 对参数进行校验
        var count = 0
        if (hide) count++
        if (loading) count++
        if (error) count++
        if (noData) count++
        if (noNetwork) count++
        if (noLogin) count++

        when {
            count == 0 -> throw IllegalArgumentException("必须设置一个参数为true")
            count > 1 -> throw IllegalArgumentException("只能有一个参数为true")
        }

        // 修改状态
        when {
            hide -> stateViewLD.value = (StateLayoutEnum.SUCCESS)
            loading -> stateViewLD.value = (StateLayoutEnum.LOADING)
            error -> stateViewLD.value = (StateLayoutEnum.ERROR)
            noData -> stateViewLD.value = (StateLayoutEnum.NO_DATA)
            noNetwork -> stateViewLD.value = (StateLayoutEnum.NO_NETWORK)
            noLogin -> stateViewLD.value = (StateLayoutEnum.NO_LOGIN)
        }
    }

}