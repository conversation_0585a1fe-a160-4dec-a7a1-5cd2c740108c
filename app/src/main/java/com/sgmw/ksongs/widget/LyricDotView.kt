package com.sgmw.ksongs.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import com.sgmw.ksongs.R

/**
 * @author: 董俊帅
 * @time: 2025/2/27
 * @desc: MV播放倒计时 自定义小圆点
 */
class LyricDotView(context: Context, attrs: AttributeSet) : View(context, attrs) {

    // 转换 22.5dp 为像素
    private val dotRadius: Float = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP,
        context.resources.getDimension(R.dimen.dp_22_5),
        context.resources.displayMetrics
    )  // 从 dp 转换为像素
    private val totalDots = 5     // 圆点的总数
    private var visibleDots = totalDots // 当前可见的小圆点数量
    private var dotColor: Int = context.getColor(R.color.bg_lyric_dot) // 默认的小圆点颜色为蓝色

    private val paint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.FILL
    }

    private val dotAlpha = FloatArray(totalDots) { 1f }  // 存储每个圆点的透明度，默认全不透明

    init {
        // 从属性中获取圆点颜色（如果有的话）
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.LyricDotView)
        dotColor = typedArray.getColor(R.styleable.LyricDotView_dotColor, dotColor)
        typedArray.recycle()

        // 设置 paint 颜色
        paint.color = dotColor
    }

    // 设置圆点颜色
    fun setDotColor(color: Int) {
        dotColor = color
        paint.color = dotColor  // 设置 paint 颜色
        invalidate()  // 重绘
    }

    // 外部调用倒计时更新的方法
    fun updateDotsBasedOnCountDown(countDownMillis: Int) {
        // 根据倒计时的剩余时间决定显示的小圆点数量，并设置渐隐动画
        visibleDots = calculateVisibleDots(countDownMillis)
        updateDotAlpha(countDownMillis)
        invalidate() // 更新视图
    }

    // 计算应该显示的圆点数量
    private fun calculateVisibleDots(countDownMillis: Int): Int {
        // 根据倒计时毫秒数计算显示的点数
        // 1000-1999毫秒显示1个点，2000-2999毫秒显示2个点，以此类推
        val seconds = countDownMillis / 1000 // 直接取整数部分
        val totalDotsToShow = maxOf(0, minOf(seconds, totalDots))
        return totalDotsToShow
    }

    // 更新每个圆点的透明度
    private fun updateDotAlpha(countDownMillis: Int) {
        // 所有圆点都设置为完全可见，不需要渐隐效果
        dotAlpha.fill(1f)
    }

    // 计算圆点的间距，间隔固定为 40f
    val spacing = 24f  // 圆点之间的间隔

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val width = spacing * visibleDots + dotRadius * 2 * visibleDots
        setMeasuredDimension(width.toInt(), heightMeasureSpec)
    }

    // 绘制小圆点
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)


        // Y 轴的位置，垂直居中
        val y = height / 2f

        // 根据可见圆点数量绘制圆点
        for (i in 0 until visibleDots) {
            paint.alpha = (dotAlpha[i] * 255).toInt() // 设置透明度
            val startX = spacing * (i + 1) + dotRadius * 2 * i // 从左到右排列
            canvas.drawCircle(startX, y, dotRadius, paint)
        }
    }

    // 添加恢复所有圆点显示的方法
    fun resetDots() {
        // 重置所有小圆点的透明度为1（完全可见）
        dotAlpha.fill(1f)
        visibleDots = totalDots // 设置为显示所有圆点
        invalidate() // 重绘视图
    }
}






