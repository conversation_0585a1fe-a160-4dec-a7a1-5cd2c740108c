package com.sgmw.ksongs.widget;

import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;

import androidx.core.widget.NestedScrollView;
import androidx.interpolator.view.animation.FastOutSlowInInterpolator;

import com.sgmw.ksongs.R;
import com.sgmw.ksongs.utils.SendBroadCastUtil;


/**
 * 上下拉回弹的ScrollView， 适用于内部没有可滑动View的嵌套
 * 部分代码来自CustomScrollView
 */
public class CommonBounceScrollView extends NestedScrollView {

    private static final String TAG = "CommonBounceScrollView";
    private Context ctx;

    private float initialY;
    private float finalY;
    public CommonBounceScrollView(Context context) {
        this(context, null);
        ctx = context;
    }

    public CommonBounceScrollView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
        ctx = context;
    }

    public CommonBounceScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        ctx = context;
        init(context);
    }

    @SuppressLint("NewApi")
    private void init(Context context) {
        setFadingEdge();
    }

    private void setFadingEdge(){
        setVerticalFadingEdgeEnabled(true);
        setFadingEdgeLength(getResources().getDimensionPixelSize(R.dimen.dp_80));
    }

    @Override
    protected float getTopFadingEdgeStrength() {
        //与RecyclerView的顶部阴影渐变效果计算方式保持一致
        float ratio = super.getTopFadingEdgeStrength();
        if (ratio > 0f) {
            ratio = 1f;
        } else {
            ratio = 0f;
        }
        return ratio;
    }

    @Override
    protected float getBottomFadingEdgeStrength() {
        //与RecyclerView的底部阴影渐变效果计算方式保持一致
        float ratio = super.getBottomFadingEdgeStrength();
        if (ratio > 0f) {
            ratio = 1f;
        } else {
            ratio = 0f;
        }
        return ratio;
    }

//    @Override
//    public boolean dispatchTouchEvent(MotionEvent event) {
//        switch (event.getAction()) {
//            case MotionEvent.ACTION_DOWN:
//                Log.i(TAG, "-------MotionEvent.ACTION_DOWN:event.getY()" + event.getY());
//                initialY = event.getY();
//                break;
//            case MotionEvent.ACTION_UP:
//                Log.i(TAG, "-------MotionEvent.ACTION_UP:event.getY()" + event.getY());
//                finalY = event.getY();
//                if (finalY - initialY > 0) {
//                    Utils.sendSlidTopBroadCast(getContext());
//                } else if (finalY - initialY < 0) {
//                    Utils.sendSlidBottomBroadCast(getContext());
//                }
//                break;
//        }
//        return super.dispatchTouchEvent(event);
//    }

    private int startX, startY;

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                Log.i(TAG, "-------MotionEvent.ACTION_DOWN:event.getY()" + ev.getY());
                initialY = ev.getY();
                startX = (int) ev.getX();
                startY = (int) ev.getY();
                if (enableBoundAnimation) {
                    getParent().requestDisallowInterceptTouchEvent(true);//告诉viewgroup不要去拦截我
                }
                break;
            case MotionEvent.ACTION_MOVE:
                int endX = (int) ev.getX();
                int endY = (int) ev.getY();
                int disX = Math.abs(endX - startX);
                int disY = Math.abs(endY - startY);
                if (enableBoundAnimation) {
                    if (disX > disY) {
                        getParent().requestDisallowInterceptTouchEvent(false);
                    } else {
                        getParent().requestDisallowInterceptTouchEvent(true);//下拉的时候是false
                    }
                }
                break;
            case MotionEvent.ACTION_UP:
                if (enableBoundAnimation) {
                    getParent().requestDisallowInterceptTouchEvent(true);
                }
                Log.i(TAG, "-------MotionEvent.ACTION_UP:event.getY()" + ev.getY());
                finalY = ev.getY();
                if (finalY - initialY > 0) {
                    SendBroadCastUtil.INSTANCE.sendSlidTopBroadCast(getContext());
                } else if (finalY - initialY < 0) {
                    SendBroadCastUtil.INSTANCE.sendSlidBottomBroadCast(getContext());
                }
                break;
            case MotionEvent.ACTION_CANCEL:
                if (enableBoundAnimation) {
                    getParent().requestDisallowInterceptTouchEvent(true);
                }
                break;
        }
        return super.dispatchTouchEvent(ev);
    }



    private View childView;

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        if (getChildCount() > 0) {
            childView = getChildAt(0);
        }
    }

    // 记录手指上一次的 Y 坐标
    private float mLastY;
    // 记录回弹偏移量
    private float mBounceOffset = 0;
    // 标记是否处于拖拽（拉伸）状态
    private boolean mIsDragging = false;

    /**
     * 是否启动回弹动画
     */
    private boolean enableBoundAnimation = true;
    private boolean canReBound = true;
    // 回弹动画时长（毫秒）
    private static final int BOUNCE_DURATION = 450;

    @Override
    public boolean onTouchEvent(MotionEvent e) {
        switch (e.getActionMasked()) {
            case MotionEvent.ACTION_MOVE:
                float nowY = e.getY();
                float deltaY = nowY - mLastY;
                mLastY = nowY;

                // 1. 如果已经在回弹中，无论方向都继续往 0 推
                if (mIsDragging) {
                    float nextOffset = mBounceOffset + deltaY / 2f;
                    // 判断是否已经反向回到或超过 0
                    if ((mBounceOffset > 0 && nextOffset <= 0) ||
                            (mBounceOffset < 0 && nextOffset >= 0)) {
                        // 够了，直接回归正常状态
                        mBounceOffset = 0;
                        mIsDragging = false;
                    } else {
                        // 继续累加阻尼偏移
                        mBounceOffset = nextOffset;
                    }
                    invalidate();
                    return true;
                }

                // 2. 未在回弹时，按原逻辑判断进/出界触发回弹
                boolean isPullDown = !canScrollVertically(-1) && deltaY > 0;
                boolean isPullUp = !canScrollVertically(1) && deltaY < 0;

                // 内容不足一屏时，不允许向上回弹
                int contentHeight = getContentHeight();
                int viewHeight = getHeight();
                canReBound = (contentHeight > viewHeight || !isPullUp) && enableBoundAnimation;
                if (canReBound && (isPullDown || isPullUp)) {
                    mIsDragging = true;
                    mBounceOffset += deltaY / 2f;
                    invalidate();
                    return true;
                }
                break;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                if (mIsDragging) {
                    // 松手后做一个平滑回弹动画
                    ValueAnimator animator = ValueAnimator.ofFloat(mBounceOffset, 0);
                    animator.setDuration(BOUNCE_DURATION);
                    animator.setInterpolator(new FastOutSlowInInterpolator());
                    animator.addUpdateListener(animation -> {
                        mBounceOffset = (float) animation.getAnimatedValue();
                        invalidate();
                    });
                    animator.start();
                    mIsDragging = false;
                    return true;
                }
                break;
        }
        return super.onTouchEvent(e);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent e) {
        if (e.getActionMasked() == MotionEvent.ACTION_DOWN) {
            // 无论子 View 消不消费，先记录起点
            mLastY = e.getY();
            mIsDragging = false;
        }
        return super.onInterceptTouchEvent(e);
    }

    @Override
    protected void dispatchDraw(android.graphics.Canvas canvas) {
        if (!canReBound) {
            super.dispatchDraw(canvas);
            return;
        }
        int saveCount = canvas.save();
        // 应用上下平移效果，但在绘制前裁剪到原始 RecyclerView 边界
        canvas.translate(0, mBounceOffset);
        canvas.clipRect(0, 0, getWidth(), getContentHeight() + getPaddingTop() + getPaddingBottom());
        super.dispatchDraw(canvas);
        canvas.restoreToCount(saveCount);
    }

    private int getContentHeight() {
        return childView.getHeight();
    }
}
