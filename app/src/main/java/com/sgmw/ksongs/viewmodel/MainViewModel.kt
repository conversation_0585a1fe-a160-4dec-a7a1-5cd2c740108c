package com.sgmw.ksongs.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.MMKVUtils
import com.sgmw.common.utils.ioLaunch
import com.sgmw.common.utils.mainLaunch
import com.sgmw.ksongs.constant.MMKVConstant
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.manager.PermissionHelper
import com.sgmw.ksongs.model.bean.CategoryBean
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.Result
import com.sgmw.ksongs.model.bean.SearchResultBean
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.repository.SearchRepository
import com.sgmw.ksongs.model.repository.SongStationRepository
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.track.ChannelType
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.ui.songplay.VipAndLimitManager
import com.sgmw.ksongs.utils.NavigationUtils
import com.tme.ktv.player.api.KaraokePlayRequest
import com.tme.ktv.video.api.VideoState
import kotlinx.coroutines.launch


class MainViewModel : BaseViewModel() {

    val mDemandSongList = PlayListManager.getAllDemandSongInfoWithPlayingLiveData()

    private val mSearchRepository by lazy {
        SearchRepository()
    }

    val mSearchResultBean = MutableLiveData<Result<SearchResultBean?>>()

    val nextPlaySongInfo = MutableLiveData<DemandSongInfo?>()

    val currentAndNextSongInfo = MutableLiveData<Pair<DemandSongInfo?, DemandSongInfo?>>()

    /**
     * 跳转到歌曲详情页面
     */
    val goSongInfoValue = MutableLiveData<Triple<Boolean, Boolean, SongInfoBean?>>()

    /**
     * 权限改变LiveData
     */
    val isPermissionChange = MutableLiveData<Boolean>()

    /**
     * 歌词数据
     */
    val lyricLiveData = MutableLiveData<KaraokePlayRequest>()

    /**
     * 当前正在展示的某行歌词
     */
    val lyricStrLiveData = MutableLiveData<String>()

    /**
     * 歌词倒计时
     */
    val lyricCountDown = MutableLiveData<Int>()

    val changePageLiveData = MutableLiveData<Int>()

    // 播放详情页面是否展示
    val isSongPlayViewShow = MutableLiveData(false)

    fun getCurrentAndNextSongInfo() {
        viewModelScope.launch {
            val currentPlaySongInfo = PlayListManager.getPlayingSongInfo()
            val nextPlaySongInfo = PlayListManager.getNextPlaySongInfo()
            Log.d(
                TAG,
                "getCurrentAndNextSongInfo first: ${currentPlaySongInfo == null} second: ${nextPlaySongInfo == null}"
            )
            currentAndNextSongInfo.postValue(Pair(currentPlaySongInfo, nextPlaySongInfo))
        }
    }

    fun getNextPlaySongInfo() {
        viewModelScope.launch {
            val nextSong = PlayListManager.getNextPlaySongInfo()
            nextPlaySongInfo.postValue(nextSong)
        }
    }

    fun isAgreePrivacy(): Boolean {
        return MMKVUtils.getBoolean(MMKVConstant.IS_AGREE_PRIVACY, false)
    }

    fun isPlaying(): Boolean {
        return KaraokePlayerManager.getPlayer().isPlaying
    }

    /**
     * 处理播放按钮播放逻辑
     * 当前非播放状态，当前无播放歌曲 则播放下一首
     * 当前非播放状态，有播放歌曲，但是播放状态为 state_IDLE 则从头播放
     * 当钱非播放状态，有播放歌曲，则回复播放
     */
    fun handlePlay() {
        ioLaunch {
            val currentPlayingSongInfo = PlayListManager.getPlayingSongInfo()
            if (currentPlayingSongInfo == null) {
                val nextPlaySongInfo = PlayListManager.getNextPlaySongInfo()
                if (nextPlaySongInfo != null) {
                    // 添加VIP检查逻辑
                    checkVipAndPlaySong(nextPlaySongInfo.songInfo)
                }
            } else {
                val player = KaraokePlayerManager.getPlayer()
                Log.d(TAG, "llPlayControl playState: ${player.playState}")
                if (player.playState == VideoState.STATE_IDLE || player.playState == VideoState.STATE_ENDED) {
                    // 需要重新加载当前歌曲，也需要进行VIP检查
                    checkVipAndPlaySong(currentPlayingSongInfo.songInfo)
                } else {
                    // 只是恢复播放，不需要VIP检查
                    PermissionHelper.checkPermission {
                        Log.d(TAG, "handlePlay resume checkPermission it: $it")
                        KaraokePlayerManager.resume()
                    }
                }
            }
        }
    }

    /**
     * 检查VIP权限并播放歌曲
     */
    private fun checkVipAndPlaySong(songInfo: SongInfoBean) {
        mainLaunch {
            // 如果是VIP用户，直接播放
            if (VipAndLimitManager.isVip()) {
                PermissionHelper.checkPermission {
                    Log.d(TAG, "handlePlay VIP user checkPermission it: $it")
                    KaraokePlayerManager.loadSong(songInfo)
                }
                return@mainLaunch
            }

            // 如果歌曲需要VIP但用户不是VIP，跳转到VIP支付页面
            if (songInfo.need_vip) {
                Log.d(TAG, "handlePlay song needs VIP but user is not VIP: ${songInfo.song_name}")
                KaraokePlayerManager.jumpVipPayFragment(
                    NavigationUtils.getNavController(),
                    songInfo,
                    needShowTips = false,
                    needShowPlaying = false
                )
                return@mainLaunch
            }

            val player = KaraokePlayerManager.getPlayer()
            // 检查播放次数限制
            VipAndLimitManager.getLimitCount { totalFree, playedSongNums ->
                if (playedSongNums >= totalFree && KaraokeConsole.currSongInfo?.song_id != songInfo.song_id
                    || (player.playState == VideoState.STATE_IDLE || player.playState == VideoState.STATE_ENDED)) {
                    mainLaunch {
                        Log.d(TAG, "handlePlay reached play limit: $playedSongNums/$totalFree")
                        KaraokePlayerManager.jumpVipPayFragment(
                            NavigationUtils.getNavController(),
                            songInfo,
                            needShowPlaying = false
                        )
                    }
                } else {
                    // 通过权限检查后播放歌曲
                    PermissionHelper.checkPermission {
                        Log.d(TAG, "handlePlay 111 checkPermission it: $it")
                        KaraokePlayerManager.loadSong(songInfo)
                    }
                }
            }
        }
    }

    private val stationRepository = SongStationRepository()

    private val _categoryResultLiveData = MutableLiveData<Result<CategoryBean?>>()
    val categoryResultLiveData: LiveData<Result<CategoryBean?>> = _categoryResultLiveData


    fun getCategoryList(operation: Operation) {
        stationRepository.getCategoryList(operation) {
            _categoryResultLiveData.postValue(it)
        }
    }

    fun kSongSearch(word: String) {
        Log.d(TAG, "kSongSearch $word")
        SensorsDataManager.trackSearchEvent(BigDataConstants.CARD_NAME_SEARCH, word, ChannelType.VOICE_WAKE)
        mSearchRepository.getSearchResult(
            SearchRepository.Action.LETTER.value,
            SearchRepository.ContentFlag.CONTENT_FLAG_SONG.value,
            SearchRepository.FilterSingerArea.FILTER_SINGER_AREA_ALL.value,
            SearchRepository.FilterSingerType.FILTER_SINGER_TYPE_ALL.value,
            1,
            SearchRepository.DEFAULT_PAGE_NUM,
            word,
            Operation.NewData
        ) {
            it.onSuccess { data, operation ->
                mSearchResultBean.postValue(it)
            }.onFailure { resultCode, operation ->
                mSearchResultBean.postValue(it)
            }
        }

    }

    companion object {
        private const val TAG = "MainViewModel"

        /**
         * token失效或者被踢LiveData监听，
         * 当为true时，说明需要进入到登录页面;
         * 登录完成后，需要重置为false
         */
        private val _tokenErrorStatusLiveData = MutableLiveData<Int>()

        val tokenErrorStatusLiveData: LiveData<Int> = _tokenErrorStatusLiveData

        fun sendTokenErrorMsg(errorCode: Int) {
            Log.d(TAG, "sendTokenErrorMsg errorCode: $errorCode")
            _tokenErrorStatusLiveData.postValue(errorCode)
        }

        fun resetTokenErrorStatus() {
            Log.d(TAG, "resetTokenErrorStatus")
            _tokenErrorStatusLiveData.postValue(0)
        }

    }

}