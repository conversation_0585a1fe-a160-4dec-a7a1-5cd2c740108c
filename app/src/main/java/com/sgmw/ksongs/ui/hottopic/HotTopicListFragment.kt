package com.sgmw.ksongs.ui.hottopic

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.sgmw.common.ktx.dp2px
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.databinding.FragmentHotTopicListBinding
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.ui.adapter.RankListAdapter
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.widget.AccessibilityLinearLayoutManager
import com.sgmw.ksongs.widget.SpaceItemDecoration

/**
 * 热门专题类别页面
 */
class HotTopicListFragment : BaseFrameFragment<FragmentHotTopicListBinding, HotTopicListViewModel>() {

    companion object {

        private const val TAG = "HotTopicListFragment"

        private const val THEME_ID = "theme_id"
        private const val CARD_NAME = "card_name"
        const val DEFAULT_THEME_ID = 0

        /**
         * @param themeId 主题id
         * @param cardName 用于埋点 歌曲点击来源
         */
        fun newInstance(themeId: Int?,cardName:String): HotTopicListFragment {
            return HotTopicListFragment().apply {
                arguments = Bundle().apply {
                    putInt(THEME_ID, themeId ?: DEFAULT_THEME_ID)
                    putString(CARD_NAME, cardName)
                }
            }
        }

    }
    override fun needSkinApply() = true
    private val adapter = RankListAdapter()
    private var cardName = ""

    override fun FragmentHotTopicListBinding.initView() {
        rvTopicSongList.layoutManager = AccessibilityLinearLayoutManager(context)
        rvTopicSongList.adapter = adapter

        srl.setOnRefreshListener {
            mViewModel?.getHotTopicSongList(Operation.Refresh)
        }
        srl.setOnLoadMoreListener {
            mViewModel?.getHotTopicSongList(Operation.LoadMore)
        }
        adapter.setOnItemClickListener { _, view, position ->
            KaraokePlayerManager.playSong(this@HotTopicListFragment, adapter.getItem(position),cardName)
        }
        val themeId = arguments?.getInt(THEME_ID, DEFAULT_THEME_ID) ?: DEFAULT_THEME_ID
        cardName = arguments?.getString(CARD_NAME,"" ) ?: ""
        adapter.setCardName(cardName)
        mViewModel?.setThemeId(themeId)
    }

    private fun setErrorRetryListener() {
       mBinding?.stateLayout?.setErrorRetryClickListener {
           mBinding?.stateLayout?.showLoading()
           mViewModel?.getHotTopicSongList(Operation.NewData)
        }
    }

    override fun initObserve() {
        super.initObserve()

        mViewModel?.hotTopicSongListResult?.observe(this) {
            it.onSuccess { value, operation ->
                when (operation) {
                    Operation.NewData -> {
                        adapter.setList(value?.songs)
                        if (value?.has_more == 1) {
                            mBinding?.srl?.setNoMoreData(false)
                            mBinding?.rvTopicSongList?.setHasMoreData(true)
                            Log.d(Companion.TAG, "NewData: 设置还有更多数据")
                        } else {
                            mBinding?.srl?.setNoMoreData(true)
                            mBinding?.rvTopicSongList?.setHasMoreData(false)
                            Log.d(Companion.TAG, "NewData: 设置没有更多数据")
                        }
                    }
                    Operation.Refresh -> {
                        adapter.setList(value?.songs)
                        mBinding?.srl?.finishRefresh()
                        if (value?.has_more == 1) {
                            mBinding?.srl?.setNoMoreData(false)
                            mBinding?.rvTopicSongList?.setHasMoreData(true)
                            Log.d(Companion.TAG, "Refresh: 设置还有更多数据")
                        } else {
                            mBinding?.srl?.setNoMoreData(true)
                            mBinding?.rvTopicSongList?.setHasMoreData(false)
                            Log.d(Companion.TAG, "Refresh: 设置没有更多数据")
                        }
                    }
                    Operation.LoadMore -> {
                        adapter.addData(value?.songs ?: emptyList())
                        Log.d(Companion.TAG, "LoadMore: has_more = ${value?.has_more}")
                        if (value?.has_more == 1) {
                            mBinding?.srl?.finishLoadMore()
                            mBinding?.rvTopicSongList?.setHasMoreData(true)
                            Log.d(Companion.TAG, "LoadMore: 设置还有更多数据")
                        } else {
                            mBinding?.srl?.finishLoadMoreWithNoMoreData()
                            mBinding?.rvTopicSongList?.setHasMoreData(false)
                            Log.d(Companion.TAG, "LoadMore: 设置没有更多数据")
                        }
                    }
                    Operation.UpdateStatus -> {
                        adapter.setList(value?.songs)
                    }

                    else -> {}
                }
                if (adapter.data.isEmpty()) {
                    mBinding?.stateLayout?.showEmpty()
                } else {
                    mBinding?.stateLayout?.showContent()
                }
            }.onFailure { _, operation ->
                when (operation) {
                    Operation.NewData -> {
                        mBinding?.stateLayout?.showError()
                        setErrorRetryListener()
                    }
                    Operation.Refresh -> {
                        mBinding?.srl?.finishRefresh()
                    }
                    Operation.LoadMore -> {
                        mBinding?.srl?.finishLoadMore()
                    }
                    Operation.UpdateStatus -> {
                    }
                    else -> {}
                }
            }
        }

        KaraokeConsole.playState.observe(this) {
            adapter.notifyDataSetChanged()
        }
        mViewModel?.collectSongChangeLiveData?.observe(this) {
            if (adapter.data.isNotEmpty()) {
                mViewModel?.updateCollectStatus(adapter.data)
            }
        }
        mViewModel?.demandSongInfo?.observe(this) {
            if (adapter.data.isNotEmpty()) {
                mViewModel?.updateDemandStatus(adapter.data)
            }
        }
    }

    override fun initRequestData() {
        mViewModel?.getHotTopicSongList(Operation.NewData)
    }


}