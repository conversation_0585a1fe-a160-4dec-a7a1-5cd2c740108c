package com.sgmw.ksongs.ui.playlist

import androidx.lifecycle.LiveData
import com.sgmw.common.utils.DaemonThreadDispatcher
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.ioLaunch
import com.sgmw.common.utils.mainLaunch
import com.sgmw.ksongs.db.DbManager
import com.sgmw.ksongs.db.dao.DemandDao
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.utils.addPlayRecord
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.withContext

/**
 * @author: 董俊帅
 * @time: 2025/2/20
 * @desc: 播放列表管理类
 */
object PlayListManager {

    private const val TAG = "PlayListManager"

    /**
     * 获取DemandDao，每次都从DbManager获取最新实例，确保数据库重新初始化后能正常工作
     */
    private fun getDemandDao(): DemandDao {
        return DbManager.getDemandSongInfoDao()
    }

    private val _playRecordUpdated = MutableSharedFlow<Unit>()
    val playRecordUpdated: Flow<Unit> = _playRecordUpdated.asSharedFlow()

    /**
     * 添加歌曲到已点列表， 已添加的不再添加
     */
    fun addDemandSongInfo(demandSongInfo: DemandSongInfo, isPlayEnd: Boolean = false, cardName: String = "") {
        ioLaunch {
            try {
                val demandDao = getDemandDao()

                // 添加数据验证
                val songInfo = demandSongInfo.songInfo
                if (songInfo.song_name.isBlank() || songInfo.singer_name.isBlank()) {
                    Log.e(TAG, "尝试添加无效歌曲数据: song_id=${songInfo.song_id}, song_name='${songInfo.song_name}', singer_name='${songInfo.singer_name}'")
                    return@ioLaunch // 不添加无效数据
                }

                if (!demandDao.isSongIdExists(demandSongInfo.songInfo.song_id)) {
                    if (!isPlayEnd) {
                        demandSongInfo.songInfo.isPlaying = false
                        demandSongInfo.songInfo.isPlayingState = false
                    }
                    demandDao.insertDemandSongInfo(demandSongInfo)
                    SungListManager.removeFromSungList(demandSongInfo.songInfo)
                    // 下方是埋点
                    SensorsDataManager.trackAddDemandClickEvent(cardName, demandSongInfo.songInfo.song_name, demandSongInfo.songInfo.singer_name)
                } else {
                    demandDao.deleteDemandSongInfo(demandSongInfo.songInfo.song_id)
                }
            } catch (e: Exception) {
                Log.e(TAG, "addDemandSongInfo failed for ${demandSongInfo.songInfo.song_name}", e)
            }
        }
    }

    /**
     * 获取当前已点列表中的所有歌曲
     */
    suspend fun getDemandSongInfoList(): MutableList<DemandSongInfo> {
        return getDemandDao().getAllDemandSongInfo()
    }

    /**
     * 获取当前已点列表中的所有歌曲除去正在播放的歌曲
     */
    suspend fun getDemandSongInfoListWithOutPlaying(): MutableList<DemandSongInfo> {
        return getDemandDao().getAllDemandSongInfoWithOutPlaying()
    }

    /**
     * 获取正在播放的歌曲
     */
    fun getPlayingSongInfoLiveData(): LiveData<DemandSongInfo?> {
        return getDemandDao().getPlayingSongInfoLiveData()
    }

    /**
     * 获取正在播放的歌曲
     */
    suspend fun getPlayingSongInfo(): DemandSongInfo? {
        return getDemandDao().getPlayingSongInfo()
    }

    /**
     * 将歌曲添加到已点列表的顶部
     */
    suspend fun addTopSongInfo(songInfo: DemandSongInfo) {
        // 添加数据验证
        val songInfoBean = songInfo.songInfo
        if (songInfoBean.song_name.isNullOrBlank() || songInfoBean.singer_name.isNullOrBlank()) {
            Log.e(TAG, "尝试添加无效歌曲数据到顶部: song_id=${songInfoBean.song_id}, song_name='${songInfoBean.song_name}', singer_name='${songInfoBean.singer_name}'")
            return // 不添加无效数据
        }

        val demandDao = getDemandDao()
        val topDemandSongInfo = demandDao.getTopDemandSongInfo()
        val currentTime = topDemandSongInfo?.insertTime ?: System.currentTimeMillis()
        val songInfoById = demandDao.getSongInfoById(songInfo.songInfo.song_id)
        if (songInfoById != null) {
            demandDao.deleteDemandSongInfo(songInfoById)
        }
        songInfo.insertTime = currentTime - 1
        demandDao.insertDemandSongInfo(songInfo)
        SungListManager.removeFromSungList(songInfo.songInfo)
    }

    /**
     * 删除所有的已点歌曲
     */
    suspend fun deleteAllDemandSongInfo() {
        getDemandDao().deleteAllDemandSongInfo()
    }

    /**
     * 获取下一首要播放的歌曲信息
     */
    suspend fun getNextPlaySongInfo(): DemandSongInfo? {
        return try {
            // 使用数据库专用线程池，避免阻塞主线程
            withContext(DaemonThreadDispatcher.Database) {
                val songInfoList = getDemandDao().getAllDemandSongInfoWithOutPlaying()
                if (songInfoList.isEmpty()) {
                    null
                } else {
                    val songInfo = songInfoList[0]
                    Log.d(TAG, "getNextPlaySongInfo song_name: ${songInfo.songInfo.song_name}")
                    songInfo
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getNextPlaySongInfo error: ${e.message}", e)
            null
        }
    }

    /**
     * 删除已点歌曲
     */
    suspend fun deleteDemandSongInfoById(songId: String) {
        try {
            // 使用数据库专用线程池，避免阻塞主线程
            withContext(DaemonThreadDispatcher.Database) {
                getDemandDao().deleteDemandSongInfo(songId)
            }
        } catch (e: Exception) {
            Log.e(TAG, "deleteDemandSongInfoById error: ${e.message}", e)
        }
    }

    /**
     * 随机打乱已点列表
     */
    suspend fun shuffleDemandList() {
        val list = getDemandSongInfoListWithOutPlaying()
        list.shuffle()
        val baseTime = System.currentTimeMillis()
        for (i in list.indices) {
            val songInfoBean = list[i]
            // 为每个歌曲设置不同的时间戳，确保排序稳定
            songInfoBean.insertTime = baseTime + i
        }
        getDemandDao().update(list)
    }

    /**
     * 获取已点歌曲列表LiveData
     */
    fun getAllDemandSongInfoLiveData(): LiveData<MutableList<DemandSongInfo>> {
        return getDemandDao().getAllDemandSongInfoLiveData()
    }

    /**
     * 获取已点歌曲列表LiveData，包含正在播放的歌曲
     */
    fun getAllDemandSongInfoWithPlayingLiveData(): LiveData<MutableList<DemandSongInfo>> {
        return getDemandDao().getAllDemandSongInfoWithPlayingLiveData()
    }

    /**
     * 把当前歌曲设置成正在播放的歌曲
     */
    suspend fun updatePlayingSongInfo(songInfo: SongInfoBean) {
        // 添加数据验证
        if (songInfo.song_name.isNullOrBlank() || songInfo.singer_name.isNullOrBlank()) {
            Log.e(TAG, "尝试设置无效歌曲为正在播放: song_id=${songInfo.song_id}, song_name='${songInfo.song_name}', singer_name='${songInfo.singer_name}'")
            return // 不处理无效数据
        }

        val demandDao = getDemandDao()
        val playingSongInfo = demandDao.getPlayingSongInfo()
        playingSongInfo?.let {
            Log.d(TAG, "playing song_name: ${playingSongInfo.songInfo.song_name}")
            SungListManager.removeFromSungList(songInfo)
            if (songInfo.song_id != it.songInfo.song_id) {
                it.songInfo.isPlaying = false
                // 添加到已唱歌曲
                SungListManager.addToSungList(it.songInfo)
                // 添加到播放历史记录
                addPlayRecord(it.songInfo)
                // 从已点列表删除
                demandDao.deleteDemandSongInfo(it)
            }
        }
        var songInfoById = demandDao.getSongInfoById(songInfo.song_id)
        if (songInfoById != null) {
            demandDao.deleteDemandSongInfo(songInfoById)
            songInfoById.songInfo.isPlaying = true
        } else {
            songInfo.isPlaying = true
            songInfoById = DemandSongInfo(songInfo = songInfo)
        }
        demandDao.insertDemandSongInfo(songInfoById)
    }

    /**
     * 把当前歌曲设置成已经播放的歌曲
     */
    fun updateSungList(mid: String) {
        ioLaunch {
            try {
                val demandDao = getDemandDao()
                // 1. 先获取数据
                val songInfoById = demandDao.getSongInfoById(mid) ?: return@ioLaunch
                Log.d(TAG, "updateSungList song_name: ${songInfoById.songInfo.song_name}")
                // 2. 在主线程更新UI状态
                mainLaunch {
                    // 清空当前播放信息
                    KaraokeConsole.currSongInfo = null
                    KaraokeConsole.currSongInfoLiveData.value = null
                }
                // 3. 按顺序执行数据库操作
                // 删除已点歌曲
                demandDao.deleteDemandSongInfo(songInfoById)
                // 添加到已唱列表
                SungListManager.addToSungList(songInfoById.songInfo)
                // 添加播放记录
                addPlayRecord(songInfoById.songInfo)
                // 4. 发送通知
                _playRecordUpdated.emit(Unit)
            } catch (e: Exception) {
                Log.e(TAG, "updateSungList failed: ${e.message}")
            }
        }
    }

}