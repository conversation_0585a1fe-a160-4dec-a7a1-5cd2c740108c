package com.sgmw.ksongs.ui.search

import android.content.Context

import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import androidx.navigation.Navigation
import androidx.recyclerview.widget.GridLayoutManager
import com.blankj.utilcode.util.SizeUtils

import com.blankj.utilcode.util.ToastUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.chad.library.adapter.base.listener.OnLoadMoreListener
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.LayoutSearchResultBinding
import com.sgmw.ksongs.model.bean.SingerBean
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.ui.adapter.SingerInfoAdapter
import com.sgmw.ksongs.ui.singerlist.SongListBySingerFragment
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.widget.AccessibilityGridLayoutManager
import com.sgmw.ksongs.widget.SpaceItemDecoration


class SearchSingersResultView @JvmOverloads constructor(context: Context) : LinearLayout(context) {

    private val mBinding: LayoutSearchResultBinding by lazy {
        LayoutSearchResultBinding.inflate(LayoutInflater.from(context), this, true)
    }
    private val singerInfoAdapter: SingerInfoAdapter by lazy {
        SingerInfoAdapter()
    }

    private var mOnLoadMoreListener: OnLoadMoreListener? = null
    private var mHideKeyboardListener:HideKeyboardListener? = null

    init {
        initViews()
    }

    /*
    * hasMore 是否存在更多
    * isAddData 是添加数据
    * */
    fun addData(data: MutableList<SingerBean.Singer>?) {
        if (data != null) {
            singerInfoAdapter.addData(data)
            mBinding.refreshLayoutResult.finishLoadMore()
        } else {
            mBinding.refreshLayoutResult.finishLoadMoreWithNoMoreData()
        }
    }

    fun setList(data: MutableList<SingerBean.Singer>?) {
        mBinding.recyclerviewResult.scrollToPosition(0)
        if (data == null) {
            singerInfoAdapter.setList(mutableListOf())
            mBinding.stateLayout.showEmpty()
        } else {
            singerInfoAdapter.setList(data)
            mBinding.stateLayout.showContent()
        }
    }

    fun finishLoadMore(success: Boolean) {
        mBinding.refreshLayoutResult.finishLoadMore(success)
    }

    private fun initViews() {

        val layoutManager = AccessibilityGridLayoutManager(context, 7)
        mBinding.recyclerviewResult.layoutManager = layoutManager
        mBinding.recyclerviewResult.adapter = singerInfoAdapter
        mBinding.recyclerviewResult.addItemDecoration(
            SpaceItemDecoration(
                bottomSpace = SizeUtils.dp2px(24F)
            )
        )
        mBinding.recyclerviewResult.setOnTouchListener { view, motionEvent ->
            mHideKeyboardListener?.hideKeyboard()
            false
        }
        singerInfoAdapter.setNewInstance(mutableListOf())

        singerInfoAdapter.setOnItemClickListener { adapter, v, position ->
            mHideKeyboardListener?.hideKeyboard()
            val item = singerInfoAdapter.getItem(position)
            val cardName =  BigDataConstants.CARD_NAME_SEARCH_RESULT + BigDataConstants.SPLIT +context.getString(R.string.singer) + BigDataConstants.SPLIT + item.singer_name
            NavigationUtils.navigateSafely(
                Navigation.findNavController(v), R.id.action_search_to_song_by_singer,
                SongListBySingerFragment.createBundle(item.singer_id, item.singer_name,cardName)
            )
        }

        mBinding.refreshLayoutResult.setOnLoadMoreListener {
            mOnLoadMoreListener?.onLoadMore()
        }

        mBinding.stateLayout.showContent()
    }

    fun setOnLoadMoreListener(onLoadMoreListener: OnLoadMoreListener?){
        mOnLoadMoreListener = onLoadMoreListener
    }
    fun setHideKeyboardListener(hideKeyboardListener:HideKeyboardListener?){
        mHideKeyboardListener = hideKeyboardListener
    }

    fun release() {
        // 移除所有子视图
        parent?.let { removeAllViews() }
    }
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 视图从窗口分离时释放资源
        release()
    }


}
