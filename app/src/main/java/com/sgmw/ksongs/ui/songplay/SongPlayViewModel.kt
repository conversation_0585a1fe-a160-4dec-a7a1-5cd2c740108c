package com.sgmw.ksongs.ui.songplay

import android.app.Activity
import android.os.Handler
import android.os.Looper
import android.view.MotionEvent
import androidx.lifecycle.MutableLiveData
import com.sgmw.common.ktx.getDisplayMetrics
import com.sgmw.common.ktx.getScreenBrightness
import com.sgmw.common.ktx.toFloatRoundOne
import com.sgmw.common.mvvm.vm.BaseViewModel
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.MMKVUtils
import com.sgmw.common.utils.ioLaunch
import com.sgmw.ksongs.constant.Constant
import com.sgmw.ksongs.constant.MMKVConstant
import com.sgmw.ksongs.manager.GlobalConfigHelper
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.model.repository.CollectRepository
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.utils.collectOrCancelSongInfo
import com.sgmw.ksongs.widget.VideoGestureConstraintLayout
import com.tme.ktv.player.api.KaraokePlayer
import kotlin.math.abs

/**
 * @author: 董俊帅
 * @time: 2025/2/11
 * @desc:
 */
class SongPlayViewModel : BaseViewModel(), VideoGestureConstraintLayout.VideoGestureListener {

    companion object {
        private const val TAG = "SongPlayViewModel"
    }

    /**
     * 亮度手势回调接口
     */
    interface BrightnessGestureCallback {
        fun onBrightnessGestureEnd()
    }

    // 亮度手势回调
    private var brightnessCallback: BrightnessGestureCallback? = null

    /**
     * 设置亮度手势回调
     */
    fun setBrightnessGestureCallback(callback: BrightnessGestureCallback?) {
        this.brightnessCallback = callback
    }

    private val player by lazy {
        KaraokePlayerManager.getPlayer()
    }

    private val collectRepository by lazy {
        CollectRepository()
    }

    val preLoadProgress = KaraokeConsole.preLoadProgress
    val songInfoLiveData = KaraokeConsole.currSongInfoLiveData
    val playingSongInfoLiveData = PlayListManager.getPlayingSongInfoLiveData()
    val playState = KaraokeConsole.playState
    val currPlayError = KaraokeConsole.currPlayError
    val shouldShowPicUrl = KaraokeConsole.shouldShowPicUrl
    val currentTime = KaraokeConsole.currentTime
    val isPitcherOpen = KaraokeConsole.isPitcherOpen
    val allDemandSongInfoLiveData = PlayListManager.getAllDemandSongInfoLiveData()
    val allDemandSongInfoWithPlayingLiveData = PlayListManager.getAllDemandSongInfoWithPlayingLiveData()

    val isVocalsOpen = MutableLiveData<Boolean>()
    val isMvOpen = KaraokeConsole.isMvOpen
    val isScoreOpen = MutableLiveData<Boolean>()
    val isCollect = MutableLiveData<Boolean>()
    val showControlBar = MutableLiveData(false)

    // 外部主APP的屏幕亮度
    private var mMainScreenLight = 0.0f

    private val handler = Handler(Looper.getMainLooper())
    private var hideRunnable: Runnable? = null
    private val hideDelay = 5000L // 5秒

    /**
     * 获取原唱/伴奏状态
     */
    fun getVocalsStatus(): Boolean {
        return MMKVUtils.getBoolean(MMKVConstant.VOCALS_OPEN_STATUE, true)
    }

    /**
     * 初始化原唱/伴奏状态
     */
    fun initVocalsStatus() {
        val vocalsStatus = getVocalsStatus()
        val dataSourceType = if (vocalsStatus) {
            KaraokePlayer.DataSourceType.ORIGINAL
        } else {
            KaraokePlayer.DataSourceType.ACCOMPANY
        }
        player.setDataSourceType(dataSourceType)
        isVocalsOpen.postValue(vocalsStatus)
        Log.d(TAG, "initVocalsStatus dataSourceType: $dataSourceType vocalsStatus: $vocalsStatus")
    }

    /**
     * 切换原唱/伴奏
     */
    fun switchVocals() {
        val vocalsState = getVocalsStatus()
        Log.d(TAG, "switchVocals vocalsState: $vocalsState")
        val newVocalsStatus = !vocalsState
        MMKVUtils.putBoolean(MMKVConstant.VOCALS_OPEN_STATUE, newVocalsStatus)
        val dataSourceType = if (newVocalsStatus) {
            KaraokePlayer.DataSourceType.ORIGINAL
        } else {
            KaraokePlayer.DataSourceType.ACCOMPANY
        }
        Log.d(TAG, "switchVocals dataSourceType: $dataSourceType")
        player.setDataSourceType(dataSourceType)
        isVocalsOpen.postValue(newVocalsStatus)

        SensorsDataManager.trackClickEvent(
            BigDataConstants.EVENT_CODE_WESING_ORIGINAL_ACCOMPANY_SONG_SWITCH,
            BigDataConstants.EVENT_NAME_WESING_ORIGINAL_ACCOMPANY_SONG_SWITCH,
            newpPropertiesInfo = mapOf(
                BigDataConstants.SET_VALUE to if (newVocalsStatus) "1" else "0"
            )
        )
    }

    fun initMvStatus() {
        val mvOpenStatus = getMvOpenStatus()
        KaraokeConsole.isMvOpen.postValue(mvOpenStatus)
    }

    /**
     * 获取MV开关状态
     */
    fun getMvOpenStatus(): Boolean {
        val mvOpenStatus = MMKVUtils.getBoolean(MMKVConstant.MV_OPEN_STATUE, true)
        Log.d(TAG, "getMvOpenStatus mvOpenStatus: $mvOpenStatus")
        return mvOpenStatus
    }

    /**
     * 切换MV开关
     */
    fun switchMv() {
        val mvOpenStatus = getMvOpenStatus()
        val newMvOpenStatus = !mvOpenStatus
        Log.d(TAG, "switchMv newMvOpenStatus: $newMvOpenStatus")
        MMKVUtils.putBoolean(MMKVConstant.MV_OPEN_STATUE, newMvOpenStatus)
        KaraokeConsole.isMvOpen.postValue(newMvOpenStatus)
        SensorsDataManager.trackClickEvent(
            BigDataConstants.EVENT_CODE_WESING_LYRIC_MV_SWITCH,
            BigDataConstants.EVENT_NAME_WESING_LYRIC_MV_SWITCH,
            newpPropertiesInfo = mapOf(
                BigDataConstants.SET_VALUE to if (newMvOpenStatus) "1" else "0"
            )
        )
    }

    /**
     * 获取打分开关状态
     */
    fun getScoreOpenStatus(): Boolean {
        val scoreOpenStatus = MMKVUtils.getBoolean(MMKVConstant.SCORE_OPEN_STATUE, false)
        Log.d(TAG, "getScoreOpenStatus scoreOpenStatus: $scoreOpenStatus")
        return scoreOpenStatus
    }

    /**
     * 初始化打分开关
     */
    fun initScoreStatus() {
        val isOpen = getScoreOpenStatus()
        isScoreOpen.postValue(isOpen)
        Log.d(TAG, "initScoreStatus isOpen: $isOpen")
    }

    /**
     * 切换打分开关
     */
    fun switchScore(isOpen: Boolean) {
        Log.d(TAG, "switchScore isOpen: $isOpen")
        MMKVUtils.putBoolean(MMKVConstant.SCORE_OPEN_STATUE, isOpen)
        isScoreOpen.postValue(isOpen)
        SensorsDataManager.trackClickEvent(
            BigDataConstants.EVENT_CODE_WESING_SCORE_SET,
            BigDataConstants.EVENT_NAME_WESING_SCORE_SET,
            newpPropertiesInfo = mapOf(
                BigDataConstants.SET_VALUE to if (isOpen) "1" else "0"
            )
        )
    }

    /**
     * 初始化音准器开关
     */
    fun initPitcherStatus() {
        val isOpen = getPitcherOpenStatus()
        KaraokeConsole.isPitcherOpen.postValue(isOpen)
        Log.d(TAG, "initPitcherStatus isOpen: $isOpen")
    }

    /**
     * 获取音准器开关状态
     */
    private fun getPitcherOpenStatus(): Boolean {
        val pitcherOpenStatus = MMKVUtils.getBoolean(MMKVConstant.PITCHER_OPEN_STATUE, true)
        Log.d(TAG, "getPitcherOpenStatus pitcherOpenStatus: $pitcherOpenStatus")
        return pitcherOpenStatus
    }

    /**
     * 切换音准器开关
     */
    fun switchPitcher() {
        val pitcherOpenStatus = getPitcherOpenStatus()
        val newPitcherOpenStatus = !pitcherOpenStatus
        Log.d(TAG, "switchPitcher newPitcherOpenStatus: $newPitcherOpenStatus")
        MMKVUtils.putBoolean(MMKVConstant.PITCHER_OPEN_STATUE, newPitcherOpenStatus)
        KaraokeConsole.isPitcherOpen.postValue(newPitcherOpenStatus)
        SensorsDataManager.trackClickEvent(
            BigDataConstants.EVENT_CODE_WESING_PITCH_TUNER_SET,
            BigDataConstants.EVENT_NAME_WESING_PITCH_TUNER_SET,
            newpPropertiesInfo = mapOf(
                BigDataConstants.SET_VALUE to if (newPitcherOpenStatus) "1" else "0"
            )
        )
    }

    /**
     * 初始化收藏状态
     */
    fun initCollectionState(songInfo: SongInfoBean) {
        ioLaunch {
            val collectionBean = collectRepository.find(songInfo.song_id)
            Log.d(TAG, "initCollectionState collectionBean: ${collectionBean != null}")
            isCollect.postValue(collectionBean != null)
        }
    }

    /**
     * 切换收藏状态
     */
    fun switchCollect(songInfo: SongInfoBean) {
        collectOrCancelSongInfo(songInfo) {
            isCollect.postValue(it)
        }
    }

    fun initVolume() {
        val volume = KaraokePlayerManager.getAccompanyVolume()
        Log.d(TAG, "getVolume volume: $volume")
        volumeData.postValue(volume)
    }

    /**
     * 调节伴奏音量
     */
    fun setVolume(volume: Int) {
        KaraokePlayerManager.setAccompanyVolume(volume)
    }

    fun getVolume(): Int {
        val volume = KaraokePlayerManager.getAccompanyVolume()
        Log.d(TAG, "getVolume volume: $volume")
        return volume
    }

    // 初始化屏幕亮度，恢复原来设置的屏幕亮度
    fun initScreenBrightness(activity: Activity): Int {
        mMainScreenLight = activity.getScreenBrightness()
        var oldBrightness = MMKVUtils.getInt(MMKVConstant.PLAY_SCREEN_BRIGHTNESS, Constant.MAX_BRIGHTNESS / 2)
        if (oldBrightness == 0) {
            oldBrightness = Constant.MAX_BRIGHTNESS / 10
        }
        val toFloatRoundOne = (oldBrightness / Constant.MAX_BRIGHTNESS.toFloat()).toFloatRoundOne()
        Log.d(TAG, "onBrightnessGesture: toFloatRoundOne: $toFloatRoundOne")
        screenData.postValue(toFloatRoundOne)
        return oldBrightness
    }

    /**
     * 重置屏幕亮度，回到主APP的亮度
     */
    fun resetScreenBrightness(activity: Activity) {
        GlobalConfigHelper.setAppBrightness(mMainScreenLight, activity)
    }

    var brightnessData = MutableLiveData<Int>()
    var screenData = MutableLiveData<Float>()
    private var oldBrightness = 0
    private var newBrightness = 0
    private val scaleBrightness = getDisplayMetrics().heightPixels / Constant.MAX_BRIGHTNESS.toFloat()


    var volumeData = MutableLiveData<Int>()
    private var oldVolume = 0
    private var newVolume = 0
    private val maxVolume = Constant.MAX_VOLUME
    private val scaleVolume = getDisplayMetrics().heightPixels / maxVolume.toFloat()

    override fun onDown(e: MotionEvent) {
        Log.d(TAG, "onDown: ")
        oldBrightness = MMKVUtils.getInt(MMKVConstant.PLAY_SCREEN_BRIGHTNESS, Constant.MAX_BRIGHTNESS / 2)
        oldVolume = MMKVUtils.getInt(MMKVConstant.ACCOMPANY_VOLUME, Constant.DEFAULT_ACCOMPANY_VOLUME)
    }

    override fun onBrightnessGesture(e1: MotionEvent, e2: MotionEvent, distanceX: Float, distanceY: Float) {
        var currentScreenData = 0
        screenData.getValue()?.let {
            currentScreenData = (it * 100).toInt()
        }
        newBrightness = ((e1.y - e2.y) / scaleBrightness).toInt() + oldBrightness
        if (newBrightness < 0) {
            newBrightness = 0
        } else if (newBrightness > Constant.MAX_BRIGHTNESS) {
            newBrightness = Constant.MAX_BRIGHTNESS
        }

        //转换成进度，最大进度是100
        val newBrightnessProgress = Math.round(newBrightness * 100f / Constant.MAX_BRIGHTNESS)

        //设置实际亮度，滑动量超过0.1亮度范围才设置
        brightnessData.postValue(newBrightnessProgress)
        val preBrightness = 10
        if (abs((currentScreenData - newBrightnessProgress).toDouble()) >= preBrightness) {
            val toFloatRoundOne = (newBrightness / Constant.MAX_BRIGHTNESS.toFloat()).toFloatRoundOne()
            Log.d(TAG, "onBrightnessGesture: toFloatRoundOne: $toFloatRoundOne")
            screenData.postValue(toFloatRoundOne)
        }

    }

    override fun onEndChangeBright() {
        MMKVUtils.putInt(MMKVConstant.PLAY_SCREEN_BRIGHTNESS, newBrightness)
        // 手势结束时设置实际亮度，避免手势过程中频繁调用系统API
        if (brightnessCallback != null) {
            Log.d(TAG, "Calling brightnessCallback.onBrightnessGestureEnd()")
            brightnessCallback?.onBrightnessGestureEnd()
        } else {
            Log.w(TAG, "brightnessCallback is null, cannot call onBrightnessGestureEnd()")
        }
    }

    override fun onVolumeGesture(e1: MotionEvent, e2: MotionEvent, distanceX: Float, distanceY: Float) {
        var currentVolumeData = 0
        volumeData.getValue()?.let {
            currentVolumeData = it
        }
        newVolume = ((e1.y - e2.y) / scaleVolume).toInt() + oldVolume;
        if (newVolume < 0) {
            newVolume = 0
        } else if (newVolume > maxVolume) {
            newVolume = maxVolume
        }
        Log.d(TAG, "onVolumeGesture: newVolume: $newVolume")
        //转换成进度，最大进度是100
        val volumeProgress = Math.round(newVolume * 100f / maxVolume)
        Log.d(TAG, "onVolumeGesture: volumeProgress: $volumeProgress")
        volumeData.postValue(volumeProgress)

        //设置实际音量，滑动超过1音量才设置
        val preVolume: Int = 100 / maxVolume
        if (abs((currentVolumeData - volumeProgress).toDouble()) >= preVolume) {
            KaraokePlayerManager.setAccompanyVolume(newVolume)
        }
    }

    override fun onEndChangeVolume() {
        Log.d(TAG, "onEndChangeVolume: $newVolume")
        MMKVUtils.putInt(MMKVConstant.ACCOMPANY_VOLUME, newVolume)
        SensorsDataManager.trackClickEvent(
            BigDataConstants.EVENT_CODE_WESING_KARAOKE_VOLUME_ADJUST,
            BigDataConstants.EVENT_NAME_WESING_KARAOKE_VOLUME_ADJUST,
            newpPropertiesInfo = mapOf(
                BigDataConstants.SET_VALUE to newVolume.toString()
            )
        )
    }

    override fun onSingleTapGesture(e: MotionEvent) {
        Log.d(TAG, "onSingleTapGesture: ${showControlBar.value}")
        showControlBar.value?.let {
            showControlBar.postValue(!it)
        }
    }

    override fun onDoubleTapGesture(e: MotionEvent) {
        Log.d(TAG, "onDoubleTapGesture: ")
    }

    override fun onLongPress(e: MotionEvent) {
        Log.d(TAG, "onLongPress: ")
    }

    override fun onLongPressUP() {
        Log.d(TAG, "onLongPressUP: ")
    }

    fun resetHideTimer() {
        Log.d(TAG, "resetHideTimer: ")
        hideRunnable?.let {
            handler.removeCallbacks(it)
        }
        hideRunnable = Runnable {
            showControlBar.postValue(false)
        }
        hideRunnable?.let {
            handler.postDelayed(it, hideDelay)
        }
    }

    fun onDestroyView() {
        Log.d(TAG, "onDestroyView: ")
        handler.removeCallbacksAndMessages(hideRunnable)
        hideRunnable = null
    }


}