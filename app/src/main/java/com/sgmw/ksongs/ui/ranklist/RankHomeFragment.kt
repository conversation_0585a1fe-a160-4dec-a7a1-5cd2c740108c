package com.sgmw.ksongs.ui.ranklist

import android.os.Bundle
import android.view.View
import android.view.ViewTreeObserver
import androidx.appcompat.widget.TooltipCompat
import androidx.navigation.fragment.findNavController
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.ksongs.databinding.FragmentRankHomeBinding
import com.sgmw.ksongs.track.BigDataConstants.AGE_RANK
import com.sgmw.ksongs.track.BigDataConstants.HOT_RANK
import com.sgmw.ksongs.track.BigDataConstants.SPLIT
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.utils.RANK_DEFAULT_INDEX
import com.sgmw.ksongs.utils.RANK_POSITION_60
import com.sgmw.ksongs.utils.RANK_POSITION_DEFAULT
import com.sgmw.ksongs.utils.RANK_TYPE_AGE_LIST
import com.sgmw.ksongs.utils.RANK_TYPE_DEFAULT_LIST
import com.sgmw.ksongs.utils.RankType
import com.sgmw.ksongs.utils.RankTypeAgeAvailablePosition
import com.sgmw.ksongs.utils.RankTypeDefaultAvailablePosition
import com.sgmw.ksongs.utils.ViewPagerUtil
import com.sgmw.ksongs.utils.setupTabClickEvents

/**
 * 排行榜页面(排行榜/年代榜)
 */
class RankHomeFragment: BaseFrameFragment<FragmentRankHomeBinding, RankHomeViewModel>() {

    companion object {

        private const val RANK_TYPE = "rank_type"
        private const val FIRST_SHOW_POSITION = "first_show_position"

        fun createBundleWithDefault(@RankTypeDefaultAvailablePosition position: Int = RANK_POSITION_DEFAULT): Bundle {
            return createBundle(RANK_TYPE_DEFAULT_LIST, position)
        }

        fun createBundleWithAge(@RankTypeAgeAvailablePosition position: Int = RANK_POSITION_60): Bundle {
            return createBundle(RANK_TYPE_AGE_LIST, position)
        }

        private fun createBundle(@RankType rankType: Int, firstShowPosition: Int): Bundle {
            return Bundle().apply {
                putInt(RANK_TYPE, rankType)
                putInt(FIRST_SHOW_POSITION, firstShowPosition)
            }
        }

    }

    override fun FragmentRankHomeBinding.initView() {
        val rankType = arguments?.getInt(RANK_TYPE) ?: RANK_DEFAULT_INDEX
        val rankHomePageAdapter = RankHomePageAdapter(this@RankHomeFragment, rankType)
        viewPager.adapter = rankHomePageAdapter
        TabLayoutMediator(tabLayout, viewPager, true, true) { tab, position ->
            tab.text = rankHomePageAdapter.getTitle(position)
        }.attach()

        if (rankType == RANK_TYPE_AGE_LIST) {
            viewPager.offscreenPageLimit = rankHomePageAdapter.itemCount
            // 设置ViewPager切换动画时长为300ms
            ViewPagerUtil.setScrollAnimDuration(viewPager)
        }
        viewPager.setCurrentItem(arguments?.getInt(FIRST_SHOW_POSITION) ?: 0, false)

        if (rankType != RANK_TYPE_AGE_LIST){
            tabLayout.setupTabClickEvents(viewPager)
        }
        for (i in 0 until tabLayout.tabCount) {
            val tab = tabLayout.getTabAt(i)
            tab?.let {
                TooltipCompat.setTooltipText(it.view, null)
            }
        }
        ivBack.setOnSingleClickListener {
            findNavController().popBackStack()
        }
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener{
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let {
                    var type = when (rankType) {
                        RANK_TYPE_AGE_LIST -> {
                            AGE_RANK
                        }

                        else -> {
                            HOT_RANK
                        }
                    }
                    SensorsDataManager.trackSongStationEvent(type + SPLIT + it.text.toString())
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }

        })

    }

    override fun initObserve() {
        super.initObserve()
    }

    override fun initRequestData() {

    }


}