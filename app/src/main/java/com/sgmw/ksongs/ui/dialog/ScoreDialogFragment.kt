package com.sgmw.ksongs.ui.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.os.CountDownTimer
import android.text.TextUtils
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.MainActivity
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.DialogScoreBinding
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.viewmodel.MainViewModel

/**
 * @author: 董俊帅
 * @time: 2025/2/28
 * @desc: 唱歌完成,打分弹窗
 */

class ScoreDialogFragment : BaseBlurDialogFragment(R.layout.dialog_score) {

    //是否是重唱
    private var isReplay = false

    private var mSongInfo: SongInfoBean? = null

    private var countdownTimer: CountDownTimer? = null

    private val mBinding: DialogScoreBinding by lazy {
        DialogScoreBinding.inflate(layoutInflater)
    }

    private val mainViewModel by lazy {
        ViewModelProvider(requireActivity())[MainViewModel::class.java]
    }

//    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
//        return super.onCreateDialog(savedInstanceState).apply {
//            // 拦截返回键事件
//            setOnKeyListener { _, keyCode, event ->
//                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
//                    findNavController().popBackStack()
//                    true
//                } else {
//                    false
//                }
//            }
//        }
//    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // 设置点击外部关闭
        isCancelable = true
        dialog?.setCanceledOnTouchOutside(true)
        return mBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        initView()
    }

    private fun initData() {
        mSongInfo = arguments?.getParcelable(KEY_SCORE_DIALOG)
    }

    @SuppressLint("SetTextI18n")
    private fun initView() {
        Log.d(TAG, "initData: mSongInfo?.song_name ${mSongInfo?.song_name}")
        mBinding.tvSongName.text = mSongInfo?.song_name
        KaraokeConsole.mScore.observe(viewLifecycleOwner) {
            Log.d(TAG, "mScore: $it")
            mBinding.tvScore.text = getString(R.string.score_total) + it
        }
        KaraokeConsole.mLevel.observe(viewLifecycleOwner) {
            Log.d(TAG, "mLevel: $it")
            // 设计规定---满分就展示图片，否则展示文案
            if (TextUtils.equals(it, getString(R.string.score_SSS))) {
                mBinding.tvLevel.visibility = View.INVISIBLE
                mBinding.ivLevel.visibility = View.VISIBLE
            } else {
                mBinding.tvLevel.visibility = View.VISIBLE
                mBinding.ivLevel.visibility = View.INVISIBLE
                mBinding.tvLevel.text = it
            }
        }
        mBinding.apply {
            dialogContent.setOnSingleClickListener {
                Log.d(TAG, "dialogContent.setOnSingleClickListener")
            }
            root.setOnSingleClickListener {
                Log.d(TAG, "bgShadow.setOnSingleClickListener")
                cancelCountdown()
                dismiss()
            }
            ivBack.setOnSingleClickListener {
                cancelCountdown()
                dismiss()
            }
            btnNext.setOnSingleClickListener {
                Log.d(TAG, "btnNext.setOnSingleClickListener")
                cancelCountdown()
                // 设置标志位，避免onDismiss中重复执行
                isNextSongTriggered = true
                // 确保在执行下一首逻辑前先dismiss对话框，避免Fragment状态冲突
                dismiss()
                // 使用post确保dismiss完成后再执行下一首逻辑
                mBinding.root.post {
                    if (mBinding.btnNext.isEnabled) {
                        KaraokePlayerManager.playNextSong(isAutoPlayNext = true)
                    }
                }
            }
            btnReplay.setOnSingleClickListener {
                Log.d(TAG, "btnReplay.setOnSingleClickListener")
                mSongInfo?.let {
                    Log.d(TAG, "btnReplay song_name: ${mSongInfo?.song_name}")
                    it.isPlaying = true
                    it.isPlayingState = true
                    PlayListManager.addDemandSongInfo(DemandSongInfo(songInfo = it), isPlayEnd = true)
                    KaraokeConsole.currSongInfo = it
                    KaraokeConsole.currSongInfoLiveData.postValue(it)
                }
                isReplay = true
                cancelCountdown()
                dismiss()
            }
        }

        mainViewModel.nextPlaySongInfo.observe(this, nextSongObserver)
        mainViewModel.getNextPlaySongInfo()
    }

    private val nextSongObserver = Observer<DemandSongInfo?> {
        Log.d(TAG, "nextSongObserver: $it")
        if (it != null) {
            mBinding.btnNext.alpha = 1f
            mBinding.btnNext.isEnabled = true
            startCountdown()
        } else {
            mBinding.btnNext.alpha = 0.4f
            mBinding.btnNext.isEnabled = false
            mBinding.btnNext.text = getString(R.string.score_next)
        }
    }

    private fun startCountdown() {
        var secondsLeft = 5 // 假设倒计时5秒
        countdownTimer?.cancel()
        countdownTimer = object : CountDownTimer(5000, 1000) {
            @SuppressLint("SetTextI18n")
            override fun onTick(millisUntilFinished: Long) {
                mBinding.btnNext.text = getString(R.string.score_next_song, secondsLeft--)
            }

            override fun onFinish() {
                dismiss()
                // 这里调用执行下一首歌的逻辑
//                KaraokePlayerManager.playNextSong()
            }
        }
        countdownTimer?.start()
    }

    private fun cancelCountdown() {
        countdownTimer?.cancel()
    }

    // 添加标志位，避免重复执行下一首逻辑
    private var isNextSongTriggered = false

    override fun onDismiss(dialog: DialogInterface) {
        cancelCountdown()
        Log.d(TAG, "onDismiss isEnabled: ${mBinding.btnNext.isEnabled} isNextSongTriggered: $isNextSongTriggered")
        if (isReplay) {
            // 重唱
            KaraokePlayerManager.replay()
            isReplay = false
            return
        }
        // 如果已经通过按钮点击触发了下一首，则不再重复执行
        if (isNextSongTriggered) {
            isNextSongTriggered = false
            return
        }
        if (mBinding.btnNext.isEnabled) {
            // 有下一首歌曲，播放下一首
            KaraokePlayerManager.playNextSong(isAutoPlayNext = true)
        } else {
            // 不存在下一首，如果在播放页面，则隐藏播放页面，否则不做处理
            val isSongPlayViewShow = mainViewModel.isSongPlayViewShow.value
            Log.d(TAG, "onDismiss isSongPlayViewShow: $isSongPlayViewShow")
            if (isSongPlayViewShow == true) {
                hasNoNextScoreDialogFinish = true
                requireActivity().onBackPressedDispatcher.onBackPressed()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        cancelCountdown()
        mainViewModel.nextPlaySongInfo.removeObserver(nextSongObserver)
    }

    companion object {
        private const val TAG = "ScoreDialogFragment"
        const val KEY_SCORE_DIALOG = "key_score_dialog"

        // 没有下一首歌曲时,关闭的打分弹框
        var hasNoNextScoreDialogFinish = false
    }

}