package com.sgmw.ksongs.ui.playlist

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.TooltipCompat
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayoutMediator
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.DialogSongListBinding
import com.sgmw.ksongs.ui.dialog.BaseBlurDialogFragment
import com.sgmw.ksongs.ui.dialog.ConfirmDialogFragment
import com.sgmw.ksongs.utils.ViewPagerUtil

/**
 * @author: 董俊帅
 * @time: 2025/1/19
 * @desc: 播放列表Fragment
 */

class SongListDialogFragment : BaseBlurDialogFragment(R.layout.dialog_song_list) {

    private val mBinding: DialogSongListBinding by lazy {
        DialogSongListBinding.inflate(layoutInflater)
    }

    private val mViewModel: SongListViewModel by lazy {
        SongListViewModel()
    }

    private val mTitles = arrayListOf<String>()

    private val mFragments: MutableList<Fragment> = mutableListOf(
        AlreadyDemandFragment(),
        AlreadySungFragment()
    )

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // 设置点击外部关闭
        isCancelable = true
        dialog?.setCanceledOnTouchOutside(true)
        return mBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mTitles.add(resources.getString(R.string.demand_string))
        mTitles.add(resources.getString(R.string.sung_string))
        initView()
        initObserve()
    }

    private fun initView() {
        mBinding.dialogContent.setOnSingleClickListener {}
        mBinding.root.setOnSingleClickListener { dismiss() }
        mBinding.ivBack.setOnSingleClickListener { dismiss() }
        mBinding.viewPager.adapter = object : FragmentStateAdapter(this@SongListDialogFragment) {
            override fun createFragment(position: Int) = mFragments[position]

            override fun getItemCount() = mFragments.size
        }
        TabLayoutMediator(mBinding.tabLayout, mBinding.viewPager) { tab, position ->
            tab.text = mTitles[position]
        }.attach()
//        mBinding.tabLayout.setupTabClickEvents(mBinding.viewPager)
        // 添加页面切换监听器
        mBinding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                // 当前页面切换到 position
                Log.d("ViewPager", "当前是 Fragment $position")
                changeTabTextVisible(position)
            }
        })
        mBinding.tvClearList.setOnSingleClickListener {
            showConfirmDialog()
        }
        mBinding.tvOutOfOrder.setOnSingleClickListener {
            mFragments[0].let {
                if (it is AlreadyDemandFragment) {
                    it.shuffleDemandList()
                }
            }
        }
        changeTabTextDirectly()

        mBinding.viewPager.offscreenPageLimit = mFragments.size
        ViewPagerUtil.setScrollAnimDuration(mBinding.viewPager)
    }

    private fun initObserve() {
        mViewModel.mDemandList.observe(viewLifecycleOwner) {
            showOrHideTitleButton(it.size ==0)
            mBinding.tabLayout.getTabAt(0)?.text = mTitles[0] + "(${it.size})"
            clearTabLayoutTipText()
        }
        mViewModel.mSungList.observe(viewLifecycleOwner) {
            if (it.isNullOrEmpty()) {
                mBinding.tabLayout.getTabAt(1)?.text = mTitles[1] + "(0)"
            } else {
                mBinding.tabLayout.getTabAt(1)?.text = mTitles[1] + "(${it.size})"
            }
            clearTabLayoutTipText()
        }
        mViewModel.demandSongInfoSize.observe(this) {
           showOrHideTitleButton(it ==0)
        }
        mViewModel.getDemandListSize()
    }

    private fun showOrHideTitleButton(show:Boolean) {
        if (show) {
            mBinding.tvClearList.visibility = View.GONE
            mBinding.tvOutOfOrder.visibility = View.GONE
        } else if (mBinding?.tabLayout?.selectedTabPosition == 0) {
            mBinding.tvClearList.visibility = View.VISIBLE
            mBinding.tvOutOfOrder.visibility = View.VISIBLE
        }
    }


    /**
     * 更新 清空、乱序的展示状态
     */
    private fun changeTabTextVisible(position: Int = 0) {
        Log.d(TAG, "changeTabTextDirectly position is: $position")
        if (position != 0 && position != 1) {
            Log.e(TAG, "changeTabTextDirectly position is not exist")
        }
        mFragments[position].let {
            when (it) {
                is AlreadyDemandFragment -> {
                    mViewModel.getDemandListSize()
                }

                is AlreadySungFragment -> {
                    mBinding.tvClearList.visibility = View.GONE
                    mBinding.tvOutOfOrder.visibility = View.GONE
                }
            }
        }
    }

    /**
     * 更新 tab 的文本
     */
    private fun changeTabTextDirectly() {
        mBinding.tabLayout.getTabAt(0)?.text = mTitles[0] + "(${mViewModel.mDemandList.value?.size})"
        mBinding.tabLayout.getTabAt(1)?.text = mTitles[1] + "(${mViewModel.mSungList.value?.size ?: 0})"
        clearTabLayoutTipText()
    }

    private fun clearTabLayoutTipText() {
        for (i in 0 until mBinding.tabLayout.tabCount) {
            val tab = mBinding.tabLayout.getTabAt(i)
            tab?.let {
                TooltipCompat.setTooltipText(it.view, null)
            }
        }
    }

    private fun showConfirmDialog() {
        val confirmDialogFragment = ConfirmDialogFragment()
        // 检查 ConfirmDialogFragment 是否已经显示
        val fragment = requireActivity().supportFragmentManager.findFragmentByTag("ConfirmDialogFragment")
        if (fragment == null) {
            // 弹出 ConfirmDialogFragment
            confirmDialogFragment.show(requireActivity().supportFragmentManager, "ConfirmDialogFragment")
            // 添加点击事件
            confirmDialogFragment.setOnConfirmListener {
                mFragments[0].let {
                    if (it is AlreadyDemandFragment) {
                        it.clearDemandList()
                        confirmDialogFragment.dismiss()
                        mBinding.tvClearList.visibility = View.GONE
                    }
                }
            }
        }
    }

    companion object {
        private const val TAG = "SongListDialogFragment"
    }

}