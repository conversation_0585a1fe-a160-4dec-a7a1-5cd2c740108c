package com.sgmw.ksongs.ui.playlist

import androidx.lifecycle.MutableLiveData
import com.sgmw.ksongs.model.bean.SongInfoBean

/**
 * @author: 董俊帅
 * @time: 2025/2/24
 * @desc: 已播列表管理
 * 每次重启APP后,已播列表都会清空
 */
object SungListManager {

    private val _sungList = mutableListOf<SongInfoBean>()

    private var sungList = MutableLiveData<MutableList<SongInfoBean>>()

    fun getSungListLiveData(): MutableLiveData<MutableList<SongInfoBean>> {
        return sungList
    }

    /**
     * 添加到已播列表
     * 最新唱完的歌曲，添加到列表最上面
     * 使用synchronized确保线程安全，防止并发操作导致数据不一致
     */
    @Synchronized
    fun addToSungList(songInfoBean: SongInfoBean) {
        // 检查是否已存在相同song_id的歌曲，避免重复添加
        val existingIndex = _sungList.indexOfFirst { it.song_id == songInfoBean.song_id }
        if (existingIndex != -1) {
            return
        }
        _sungList.add(0, songInfoBean)
        sungList.postValue(_sungList.toMutableList()) // 创建副本避免外部修改
    }

    @Synchronized
    fun removeFromSungList(songInfoBean: SongInfoBean) {
        val index = _sungList.indexOfFirst { it.song_id == songInfoBean.song_id }
        if (index != -1) {
            _sungList.removeAt(index)
            sungList.postValue(_sungList.toMutableList()) // 创建副本避免外部修改
        }
    }

    @Synchronized
    fun removeFromSungListById(mid: String) {
        val index = _sungList.indexOfFirst { it.song_id == mid }
        if (index != -1) {
            _sungList.removeAt(index)
            sungList.postValue(_sungList.toMutableList()) // 创建副本避免外部修改
        }
    }

}