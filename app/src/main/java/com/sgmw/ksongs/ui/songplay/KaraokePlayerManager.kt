package com.sgmw.ksongs.ui.songplay

import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavController
import androidx.navigation.findNavController
import androidx.navigation.fragment.findNavController
import com.sgmw.common.ktx.convertMicVolumeToSdkRange
import com.sgmw.common.ktx.convertVolumeToSdkRange
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.MMKVUtils
import com.sgmw.common.utils.ioLaunch
import com.sgmw.common.utils.mainLaunch
import com.sgmw.ksongs.R
import com.sgmw.ksongs.constant.Constant
import com.sgmw.ksongs.constant.Constant.ACTION_TYPE_PLAY
import com.sgmw.ksongs.constant.Constant.ACTION_TYPE_PLAY_NEXT
import com.sgmw.ksongs.constant.MMKVConstant
import com.sgmw.ksongs.manager.AudioFocusHelper
import com.sgmw.ksongs.manager.CarManager
import com.sgmw.ksongs.manager.PermissionHelper
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.phonestatus.PhoneStatusManager
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.track.ChannelType
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.utils.showToast
import com.sgmw.ksongs.viewmodel.MainViewModel
import com.tme.ktv.audio.exceptions.PlayException
import com.tme.ktv.audio.model.SongInfoObject
import com.tme.ktv.player.PlayerManager
import com.tme.ktv.player.api.KaraokePlayer
import com.tme.ktv.player.preload.event.PlayPreLoaderEvent
import com.tme.ktv.video.api.VideoState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import ksong.support.models.song.SongType
import ksong.support.video.ktv.KtvPlayerImpl

/**
 * @author: 董俊帅
 * @time: 2025/2/13
 * @desc: 全局播放器管理类
 */
object KaraokePlayerManager {

    private const val TAG = "KaraokePlayerManager"

    private var karaokePlayer = KtvPlayerImpl()
    private val _showLimitDialog: MutableLiveData<Boolean> = MutableLiveData(false)
    private val _showPlayPage: MutableLiveData<Boolean> = MutableLiveData(false)
    val showLimitDialog: LiveData<Boolean> = _showLimitDialog

    // 播放请求状态管理，防止快速点击导致的并发问题
    @Volatile
    private var isProcessingPlayRequest = false
    private var currentPlayRequestId = 0L

    /**
     * 播放页面是否展示中
     */
    var mIsShowPlayPage = MutableLiveData(false)

    /**
     * 是否展示播放页面
     * true 展示
     * false 隐藏
     */
    val showPlayPage: LiveData<Boolean> = _showPlayPage

    fun setShowPlayPage(isShowPlayPage: Boolean) {
        mIsShowPlayPage.value = isShowPlayPage
    }

    fun isShowPlayPage(): Boolean {
        return mIsShowPlayPage.value ?: false
    }

    fun getPlayer(): KaraokePlayer {
        return karaokePlayer
    }

    /**
     * 外部调用跳转播放
     * @param fragment
     * @param songInfo
     * @param cardName 卡片名称 用于埋点 表示来源
     */
    fun playSong(fragment: Fragment, songInfo: SongInfoBean?, cardName: String = "") {
        // 防止快速点击导致的并发问题
        if (isProcessingPlayRequest) {
            Log.d(TAG, "playSong: 正在处理播放请求，忽略重复点击")
            return
        }

        val requestId = System.currentTimeMillis()
        currentPlayRequestId = requestId
        isProcessingPlayRequest = true

        // 使用IO线程处理耗时操作，避免阻塞主线程
        ioLaunch {
            try {
                // 检查请求是否仍然有效（防止被新请求覆盖）
                if (currentPlayRequestId != requestId) {
                    Log.d(TAG, "playSong: 请求已被新请求覆盖，取消执行")
                    return@ioLaunch
                }

                // 异步检查通话状态和限制状态
                val isInCall = PhoneStatusManager.isInCall()
                val isLimit = CarManager.isLimit()
                val isVip = VipAndLimitManager.isVip()

                // 切换到主线程更新UI
                withContext(Dispatchers.Main) {
                    if (isInCall) return@withContext
                    if (isLimit) {
                        _showLimitDialog.value = true
                        return@withContext
                    }

                    songInfo?.let {
                        SensorsDataManager.trackPlaySongEvent(cardName, it.song_name, it.singer_name)
                    }

                    val mainViewModel = ViewModelProvider(fragment.requireActivity())[MainViewModel::class.java]

                    if (isVip) {
                        checkPermission(mainViewModel, songInfo, requestId = requestId)
                        return@withContext
                    }

                    if (songInfo?.need_vip == true) {
                        jumpVipPayFragment(fragment.findNavController(), songInfo, false)
                        return@withContext
                    }

                    // 异步获取限制次数，避免阻塞主线程
                    VipAndLimitManager.getLimitCount { totalFree, playedSongNums ->
                        if (playedSongNums >= totalFree && KaraokeConsole.currSongInfo?.song_id != songInfo?.song_id
                            || (karaokePlayer.playState == VideoState.STATE_IDLE || karaokePlayer.playState == VideoState.STATE_ENDED)) {
                            mainLaunch {
                                jumpVipPayFragment(fragment.findNavController(), songInfo)
                            }
                        } else {
                            checkPermission(mainViewModel, songInfo, requestId = requestId)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "playSong error: ${e.message}", e)
            } finally {
                // 确保在所有情况下都重置处理状态
                isProcessingPlayRequest = false
            }
        }
    }

    fun jumpVipPayFragment(
        navController: NavController?,
        playSongInfo: SongInfoBean?,
        needShowTips: Boolean = true,
        channelType: ChannelType = ChannelType.SCREEN_CLICK,
        actionType: Int = ACTION_TYPE_PLAY,
        needReplay: Boolean = false,
        needShowPlaying: Boolean = true
    ) {
        Log.d(TAG, "jumpVipPayFragment = playSongInfo = ${playSongInfo?.song_name} === actionType = $actionType")

        // 检查当前是否已经在VIP支付页面，避免重复导航
        if (NavigationUtils.isCurrentVipPaymentFragment()) {
            Log.d(TAG, "jumpVipPayFragment: 当前已在VIP支付页面，跳过导航")
            return
        }

        if (needShowTips) {
            showToast(R.string.free_limit_tips)
        }
        navController?.let {
            NavigationUtils.navigateSafely(
                it, R.id.navigation_vip_payment,
                bundleOf(
                    Constant.KEY_SONG_INFO to playSongInfo,
                    Constant.KEY_PLAY_PAGE_STATE to isShowPlayPage(),
                    Constant.KEY_CHANNEL_TYPE to channelType.ordinal,
                    Constant.KEY_ACTION_TYPE to actionType,
                    Constant.KEY_NEED_REPLAY to needReplay,
                    Constant.KEY_NEED_SHOW_PLAYING to needShowPlaying
                )
            )
            sendPlayPageLivaData(false)
        }
    }


    /**
     * 外部调用跳转播放
     */
    fun playSong(
        activity: FragmentActivity,
        songInfo: SongInfoBean?,
        cardName: String = "",
        needReplay: Boolean = false
    ) {
        // 防止快速点击导致的并发问题
        if (isProcessingPlayRequest) {
            Log.d(TAG, "playSong(Activity): 正在处理播放请求，忽略重复点击")
            return
        }

        val requestId = System.currentTimeMillis()
        currentPlayRequestId = requestId
        isProcessingPlayRequest = true

        // 页面跳转需要在主线程中执行
        mainLaunch {
            try {
                // 检查请求是否仍然有效
                if (currentPlayRequestId != requestId) {
                    Log.d(TAG, "playSong(Activity): 请求已被新请求覆盖，取消执行")
                    return@mainLaunch
                }

                if (PhoneStatusManager.isInCall()) return@mainLaunch
                if (CarManager.isLimit()) {
                    _showLimitDialog.value = true
                    return@mainLaunch
                }
                if (!cardName.isNullOrEmpty()) {
                    songInfo?.let {
                        SensorsDataManager.trackPlaySongEvent(cardName, it.song_name, it.singer_name)
                    }
                }
                val mainViewModel = ViewModelProvider(activity)[MainViewModel::class.java]
                if (VipAndLimitManager.isVip()) {
                    checkPermission(mainViewModel, songInfo, needReplay, requestId = requestId)
                    return@mainLaunch
                }
                if (songInfo?.need_vip == true) {
                    jumpVipPayFragment(activity.findNavController(R.id.nav_host_fragment_activity_main), songInfo, false)
                    return@mainLaunch
                }
                VipAndLimitManager.getLimitCount { totalFree, playedSongNums ->
                    if (playedSongNums >= totalFree
                        && (KaraokeConsole.currSongInfo?.song_id != songInfo?.song_id
                                || (karaokePlayer.playState == VideoState.STATE_IDLE || karaokePlayer.playState == VideoState.STATE_ENDED))
                    ) {
                        mainLaunch {
                            jumpVipPayFragment(activity.findNavController(R.id.nav_host_fragment_activity_main), songInfo)
                        }
                    } else {
                        checkPermission(mainViewModel, songInfo, needReplay, requestId = requestId)
                    }
                }
            } finally {
                // 确保在所有情况下都重置处理状态
                isProcessingPlayRequest = false
            }
        }

    }

    private fun checkPermission(
        mainViewModel: MainViewModel,
        songInfo: SongInfoBean?,
        needReplay: Boolean = false,
        needShowPlaying: Boolean = true,
        requestId: Long = 0L
    ) {
        PermissionHelper.checkPermission { isGranted ->
            Log.d(TAG, "checkPermission isGranted: $isGranted")

            // 检查请求是否仍然有效（防止被新请求覆盖）
            if (requestId > 0L && currentPlayRequestId != requestId) {
                Log.d(TAG, "checkPermission: 请求已被新请求覆盖，取消执行")
                return@checkPermission
            }

            val isLimit = CarManager.isLimit()
            Log.d(TAG, "checkPermission isLimit: $isLimit")
            if (isLimit) {
                _showLimitDialog.value = true
            } else {
                mainViewModel.goSongInfoValue.postValue(Triple(needReplay, needShowPlaying, songInfo))
            }
        }
    }

    /**
     * 外部调用跳转播放
     * @param channelType 埋点类型 [屏幕操作/语音唤醒/方控操作]
     */
    fun playNextSong(channelType: ChannelType = ChannelType.SCREEN_CLICK, isAutoPlayNext: Boolean = false) {
        if (PhoneStatusManager.isInCall()) return
        val requestAudioFocus = AudioFocusHelper.requestAudioFocus()
        if (!requestAudioFocus) {
            Log.d(TAG, "playSong requestAudioFocus false")
            return
        }
        if (VipAndLimitManager.isVip()) {
            playNextWithPermission(channelType, isAutoPlayNext)
        } else {
            VipAndLimitManager.getLimitCount { totalFree, playedSongNums ->
                if (playedSongNums >= totalFree) {
                    mainLaunch {
                        val playSongInfo = PlayListManager.getNextPlaySongInfo()?.songInfo
                        Log.d(TAG, "limit == $playedSongNums  ")
                        jumpVipPayFragment(
                            NavigationUtils.getNavController(), playSongInfo, channelType = channelType,
                            actionType = ACTION_TYPE_PLAY_NEXT
                        )
                    }
                } else {
                    playNextWithPermission(channelType, isAutoPlayNext)
                }
            }
        }

    }

    private fun playNextWithPermission(
        channelType: ChannelType = ChannelType.SCREEN_CLICK,
        isAutoPlayNext: Boolean = false
    ) {
        PermissionHelper.checkPermission(isAutoPlayNext = isAutoPlayNext) { isGranted ->
            Log.d(TAG, "playNextSong isGranted: $isGranted")
            ioLaunch {
                val nextPlaySongInfo = PlayListManager.getNextPlaySongInfo()
                withContext(Dispatchers.Main) {
                    if (nextPlaySongInfo == null) {
                        Log.d(TAG, "playNextSong: 没有下一首")
                    } else {
                        Log.d(TAG, "playNextSong: 下一首")
                        if (nextPlaySongInfo?.songInfo?.need_vip == true && !VipAndLimitManager.isVip()) {
                            mainLaunch {
                                jumpVipPayFragment(
                                    NavigationUtils.getNavController(),
                                    nextPlaySongInfo.songInfo,
                                    false,
                                    channelType,
                                    ACTION_TYPE_PLAY_NEXT
                                )
                            }
                        } else {
                            loadSong(nextPlaySongInfo.songInfo)
                            //下方是埋点
                            nextPlaySongInfo?.songInfo?.let { songInfo ->
                                Log.d(
                                    TAG,
                                    "playNextSong song_name: ${songInfo.song_name} singer_name: ${songInfo.singer_name}"
                                )
                                SensorsDataManager.trackClickEvent(
                                    BigDataConstants.EVENT_CODE_WESING_SONG_NEXT,
                                    BigDataConstants.EVENT_NAME_WESING_SONG_NEXT,
                                    channelType,
                                    mapOf(
                                        BigDataConstants.CARD_NAME to songInfo.song_name,
                                        BigDataConstants.SINGER to songInfo.singer_name
                                    )
                                )
                            }
                        }


                    }
                }
            }
        }
    }

    /**
     * 加载歌曲
     */
    fun loadSong(songInfo: SongInfoBean) {
        val loadRequestId = System.currentTimeMillis()
        Log.d(TAG, "loadSong songInfo: $songInfo, requestId: $loadRequestId")
        CarManager.setIsPowerDownState(false)

        //把正在load的歌曲取消
        PlayerManager.getPlayPreloader().cancelAll()
        //停止当前歌曲播放
        karaokePlayer.stop()

        KaraokeConsole.currSongInfo = songInfo
        KaraokeConsole.currSongInfoLiveData.postValue(songInfo)
        //更新已点和已播列表
        updateList(songInfo)

        val builder = SongInfoObject.Builder()
        builder.setMid(songInfo.song_id)
        //设置歌曲类型，从SongType类取值，SongType类型详见下面介绍
        builder.setSongType(SongType.SONG_TYPE_K_SONG)
        //同步歌曲类型到播放控制台，PlayConsole为一全局单例，用于一些播放控制，如重唱、切换原伴唱等。
//        PlayConsole.get().switchPlayMode(songInfo.song_type)
        val songInfoObject = builder.build()

        // 开始加载歌曲，先把进度改成0
        KaraokeConsole.emitPreLoadProgress(0)
        //使用PlayerManager加载歌曲资源
        PlayerManager.preload(songInfoObject) { event: PlayPreLoaderEvent ->
            Log.d(TAG, "loadSong preload==== $event, requestId: $loadRequestId")
            when (event) {
                //预加载完成
                is PlayPreLoaderEvent.Success -> {
                    val song = event.songInfoObject
                    Log.d(TAG, "preload song songName: ${song?.songName} mQrcBytes: ${song?.mQrcBytes?.size}")

                    // 检查当前歌曲是否仍然是用户想要播放的歌曲
                    if (KaraokeConsole.currSongInfo?.song_id == songInfo.song_id) {
                        //进行播放
                        song?.let {
                            playSong(song)
                        }
                    } else {
                        Log.d(TAG, "preload success but song changed, skip playing. Current: ${KaraokeConsole.currSongInfo?.song_id}, Loaded: ${songInfo.song_id}")
                    }
                    KaraokeConsole.emitPreLoadProgress(100)
                }

                //预见加载的一些日志打印
                is PlayPreLoaderEvent.Trace -> {
                    Log.d(TAG, "event.tracePrint(): ${event.tracePrint()}")
                }

                //歌曲加载进度
                is PlayPreLoaderEvent.Progress -> {
                    Log.d(TAG, "event.progress: ${event.progress}")
                    KaraokeConsole.emitPreLoadProgress(event.progress)
                }

                //预加载失败
                is PlayPreLoaderEvent.Failed -> {
                    Log.e(TAG, "preload Failed $event")
                    try {
                        val playException = event.exception as PlayException
                        Log.e(TAG, "preload Failed errorCode: ${playException.errorCode}")
                        KaraokeConsole.currPlayError.postValue(playException.errorCode)
                    } catch (e: Exception) {
                        Log.e(TAG, "preload Failed ${e.message}")
                    }

                }

                // 升级SDK到3.4版本后，新增的事件
                is PlayPreLoaderEvent.DownLoadSuccess -> {
                    Log.e(TAG, "preload DownLoadSuccess $event")
                }

                else -> {
                    Log.e(TAG, "preload else $event")
                }
            }
        }
    }

    /**
     * 更新已点和已播列表
     */
    private fun updateList(songInfo: SongInfoBean) {
        ioLaunch {
            PlayListManager.updatePlayingSongInfo(songInfo)
        }
    }

    /**
     * 播放器内部调用 播放歌曲
     */
    private fun playSong(songInfo: SongInfoObject) {
        if (PhoneStatusManager.isInCall()) return
        Log.d(TAG, "playSong songName: ${songInfo.songName} mQrcBytes: ${songInfo.mQrcBytes?.size}")
        // 伴唱是否打开
        val isVocalsOpen = MMKVUtils.getBoolean(MMKVConstant.VOCALS_OPEN_STATUE, true)
        val dataSourceType = if (isVocalsOpen) {
            KaraokePlayer.DataSourceType.ORIGINAL
        } else {
            KaraokePlayer.DataSourceType.ACCOMPANY
        }

        // 伴奏音量
        val tonePitchShift = getTonePitchShift()
        // 伴奏音量
        val accompanyVolume = getAccompanyVolume()
        // 麦克风音量
        val micVolume = getMicVolume()

        //设置播放参数
        karaokePlayer
            .setTonePitchShift(tonePitchShift)
            .setDataSourceType(dataSourceType)
            .setMicVolume(micVolume.convertVolumeToSdkRange())
            .setVolume(accompanyVolume.convertMicVolumeToSdkRange())
        //调用play()
        karaokePlayer.play(songInfo)
    }

    /**
     * 设置伴奏音量
     * @param volume 0~39
     */
    fun setAccompanyVolume(volume: Int) {
        Log.d(TAG, "setVolume volume: $volume")
        if (volume < 0 || volume > 39) return
        karaokePlayer.setVolume(volume.convertVolumeToSdkRange())
        MMKVUtils.putInt(MMKVConstant.ACCOMPANY_VOLUME, volume)
    }

    /**
     * 获取伴奏音量
     */
    fun getAccompanyVolume(): Int {
        val accompanyVolume = MMKVUtils.getInt(MMKVConstant.ACCOMPANY_VOLUME, Constant.DEFAULT_ACCOMPANY_VOLUME)
        Log.d(TAG, "getVolume accompanyVolume: $accompanyVolume")
        return accompanyVolume
    }

    /**
     * 是否正在播放
     */
    fun isPlaying(): Boolean {
        Log.d(TAG, "isPlaying: ${karaokePlayer.playState}")
        return karaokePlayer.playState == VideoState.STATE_PLAYING
    }

    /**
     * 暂停播放
     */
    fun pause() {
        Log.d(TAG, "pause")
        karaokePlayer.pause()
    }

    /**
     * 停止播放
     */
    fun stop() {
        Log.d(TAG, "stop")
        try {
            karaokePlayer.stop()
            // 释放音频焦点
            AudioFocusHelper.setKaraokeEnableOff()
        } catch (e: Exception) {
            Log.e(TAG, "Error during stop", e)
        }
    }

    /**
     * 继续播放
     */
    fun resume() {
        Log.d(TAG, "resume")
        val requestAudioFocus = AudioFocusHelper.requestAudioFocus()
        if (!requestAudioFocus) {
            Log.e(TAG, "resume requestAudioFocus false")
            return
        }
        if (CarManager.isPowerDownState()) {
            replay()
        } else {
            karaokePlayer.resume()
        }

    }

    /**
     * 重播
     */
    fun replay() {
        Log.d(TAG, "replay")
        val requestAudioFocus = AudioFocusHelper.requestAudioFocus()
        if (!requestAudioFocus) {
            Log.e(TAG, "replay requestAudioFocus false")
            return
        }
        CarManager.setIsPowerDownState(false)
        KaraokeConsole.currSongInfo?.let { currSongInfo ->
            Log.d(TAG, "replay currSongInfo.song_name: ${currSongInfo.song_name}")
            loadSong(songInfo = currSongInfo)
        }
        Log.d(TAG, "replay end")
    }

    /**
     * 获取升降调的值
     */
    fun getTonePitchShift(): Int {
        val offset = MMKVUtils.getInt(MMKVConstant.TONE_PITCH_SHIFT, 0)
        Log.d(TAG, "getTonePitchShift offset: $offset")
        return offset
    }

    /**
     * 设置升降调的值
     */
    fun setTonePitchShift(offset: Int) {
        karaokePlayer.setTonePitchShift(offset)
        MMKVUtils.putInt(MMKVConstant.TONE_PITCH_SHIFT, offset)
    }

    /**
     * 获取麦克风音量
     */
    fun getMicVolume(): Int {
        val volume = MMKVUtils.getInt(MMKVConstant.MIC_VOLUME, 20)
        Log.d(TAG, "getVolume volume: $volume")
        return volume
    }

    /**
     * 设置麦克风音量
     */
    fun setMicVolume(volume: Int) {
        Log.d(TAG, "setVolume volume: $volume")
        CarManager.setKaraokeVoiceVolume(volume)
        MMKVUtils.putInt(MMKVConstant.MIC_VOLUME, volume)
    }

    /**
     * 从Vip页面返回时，进入播放页面
     */
    fun vipBackPlayInfo(
        activity: FragmentActivity,
        songInfo: SongInfoBean?,
        isShowPlayPage: Boolean,
        channelType: ChannelType?,
        actionType: Int,
        needReplay: Boolean,
        needShowPlaying: Boolean = true,
        onComplete: (() -> Unit)? = null
    ) {
        Log.d(
            TAG,
            "songInfo = $songInfo isShowPlayPage = $isShowPlayPage actionType = $actionType needReplay = $needReplay needShowPlaying = $needShowPlaying"
        )
        if (isShowPlayPage) {
            _showPlayPage.postValue(true)
        }
        VipAndLimitManager.getVipInfo { vipInfo ->
            Log.d(TAG, "vipInfo = $vipInfo  actionType = $actionType")
            if (vipInfo?.status == 1) {
                val mainViewModel = ViewModelProvider(activity)[MainViewModel::class.java]
                if (actionType == ACTION_TYPE_PLAY_NEXT) {
                    channelType?.let {
                        playNextWithPermission(channelType)
                    }
                } else if (actionType == ACTION_TYPE_PLAY) {
                    checkPermission(mainViewModel, songInfo, needReplay, needShowPlaying)
                }
            }
            // VIP状态检查完成后执行回调
            onComplete?.invoke()
        }
    }

    /**
     * 跳转到vip购买页面时，需要隐藏播放页面
     */
    private fun sendPlayPageLivaData(show: Boolean) {
        _showPlayPage.postValue(show)
    }

    /**
     * 清理播放器资源，用于应用退出时调用
     */
    fun cleanup() {
        Log.d(TAG, "cleanup start")
        try {
            // 停止播放
            stop()

            // 清理状态
            _showPlayPage.postValue(false)
            _showLimitDialog.postValue(false)
            mIsShowPlayPage.postValue(false)

            Log.d(TAG, "cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during cleanup", e)
        }
    }

}

