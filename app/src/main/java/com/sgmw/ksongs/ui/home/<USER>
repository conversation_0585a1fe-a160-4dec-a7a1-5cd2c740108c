package com.sgmw.ksongs.ui.home

import androidx.appcompat.widget.TooltipCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentHomeBinding
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.utils.ViewPagerUtil
import com.sgmw.ksongs.viewmodel.MainViewModel
import com.sgmw.ksongs.viewmodel.home.HomeViewModel

class HomeFragment : BaseFrameFragment<FragmentHomeBinding, HomeViewModel>() {

    private val mTitles = arrayOf("点歌台", "我的")

    private var tabLayoutMediator : TabLayoutMediator? = null
    override fun FragmentHomeBinding.initView() {
        mBinding?.let { binding ->
            binding.viewPager.adapter = object : FragmentStateAdapter(childFragmentManager,viewLifecycleOwner.lifecycle) {
                override fun createFragment(position: Int):Fragment {
                    // 动态创建 Fragment 实例
                    return when (position) {
                        0 -> SongStationFragment()
                        1 -> MineFragment()
                        else -> throw IllegalArgumentException("Invalid position: $position")
                    }
                }

                override fun getItemCount() = mTitles.size
            }
            binding.let {
                tabLayoutMediator =  TabLayoutMediator(it.tabLayout, it.viewPager,true,true) { tab, position ->
                    tab.text = mTitles[position]
                }
                tabLayoutMediator?.attach()
            }
            binding.tvSearch.setOnSingleClickListener {
                NavigationUtils.navigateSafely(findNavController(), R.id.action_home_to_search)
            }
            binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener{
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    tab?.let {
                        SensorsDataManager.trackSongStationEvent(it.text.toString())
                    }
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {
                }

                override fun onTabReselected(tab: TabLayout.Tab?) {
                }

            })

        }
        for (i in 0 until tabLayout.tabCount) {
            val tab = tabLayout.getTabAt(i)
            tab?.let {
                TooltipCompat.setTooltipText(tab.view, null)
            }
        }

        viewPager.offscreenPageLimit = 2
        ViewPagerUtil.setScrollAnimDuration(viewPager)
    }

    override fun initObserve() {
        ViewModelProvider(requireActivity())[MainViewModel::class.java].changePageLiveData.observe(viewLifecycleOwner){
            mBinding?.let { binding ->
                binding.viewPager.setCurrentItem(it,false)
            }
        }
        mViewModel?.let { viewModel ->
            viewModel.text.observe(this) {
            }

        }

    }

    override fun initRequestData() {

    }

    override fun needViewState() = false

}