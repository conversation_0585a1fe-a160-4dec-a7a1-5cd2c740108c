package com.sgmw.ksongs.ui.protocol

import android.graphics.Bitmap
import android.net.http.SslError
import android.os.Bundle
import android.webkit.SslErrorHandler
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.databinding.FragmentProtocolWebBinding
import com.sgmw.ksongs.viewmodel.protocol.ProtocolViewModel

class ProtocolWebFragment : BaseFrameFragment<FragmentProtocolWebBinding,ProtocolViewModel>() {

    companion object {
        private const val ARG_URL = "arg_url"

        /**
         * 创建ProtocolWebFragment实例的工厂方法
         * @param url 要加载的网页URL
         * @return ProtocolWebFragment实例
         */
        fun newInstance(url: String): ProtocolWebFragment {
            val fragment = ProtocolWebFragment()
            val args = Bundle()
            args.putString(ARG_URL, url)
            fragment.arguments = args
            return fragment
        }
    }

    private val TAG = ProtocolWebFragment::class.java.simpleName
    private var mUrl: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 从arguments中获取URL参数
        try {
            arguments?.let { args ->
                mUrl = args.getString(ARG_URL, "")
            }

            // 如果URL为空，记录错误日志
            if (mUrl.isEmpty()) {
                Log.e(TAG, "URL parameter is empty or null, Fragment may not work properly")
            } else {
                Log.d(TAG, "Fragment initialized with URL: $mUrl")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting URL parameter from arguments", e)
            mUrl = ""
        }
    }

    override fun needSkinApply() = true

    private var isErrorOccurred = false
    override fun FragmentProtocolWebBinding.initView() {

        // 检查URL是否有效
        if (mUrl.isEmpty()) {
            Log.e(TAG, "Cannot load WebView: URL is empty")
            mBinding?.stateLayout?.showError()
            return
        }

        val webSettings = webView.getSettings()
        webSettings.javaScriptEnabled = true

//        webView.setBackgroundColor(webView.context.getColor(R.color.mic_connection_guide_right_bg_color)) // 设置背景颜色为透明

        //不支持屏幕缩放
        webSettings.setSupportZoom(false)
        webSettings.builtInZoomControls = true
        webSettings.displayZoomControls = false
        webSettings.defaultTextEncodingName = "UTF-8"

        // 设置允许访问文件数据
        webSettings.allowFileAccess = true
        webSettings.allowContentAccess = true

        //本地没有缓存时从网络上获取
        webSettings.cacheMode = WebSettings.LOAD_NO_CACHE

        // 开启 DOM storage API 功能
        webSettings.domStorageEnabled = true
       //开启 database storage API 功能
        webSettings.databaseEnabled = true
        //允许混合加载 http与https 内容
        webSettings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW

        webView.webViewClient = object : WebViewClient() {
            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                Log.d(TAG,"onPageStarted $url")
                isErrorOccurred = false
//                webView.setBackgroundColor(Color.TRANSPARENT) // 设置背景颜色为透明
                mBinding?.stateLayout?.showLoading()
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                Log.d(TAG,"onPageFinished $url")
//                webView.setBackgroundColor(Color.TRANSPARENT) // 设置背景颜色为透明
                if (!isErrorOccurred){
                    mBinding?.stateLayout?.showContent()
                }
            }

            override fun onLoadResource(view: WebView?, url: String?) {
                super.onLoadResource(view, url)
                Log.d(TAG,"onLoadResource")
            }

            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                super.onReceivedError(view, request, error)
                Log.d(TAG,"onReceivedError")
                isErrorOccurred = true
                mBinding?.stateLayout?.showError()
            }

            override fun onReceivedSslError(
                view: WebView?,
                handler: SslErrorHandler?,
                error: SslError?
            ) {
                super.onReceivedSslError(view, handler, error)
                Log.d(TAG,"onReceivedSslError")
                mBinding?.stateLayout?.showError()

            }

            override fun onReceivedHttpError(
                view: WebView?,
                request: WebResourceRequest?,
                errorResponse: WebResourceResponse?
            ) {
                super.onReceivedHttpError(view, request, errorResponse)
                Log.d(TAG,"onReceivedHttpError")
                mBinding?.stateLayout?.showError()
            }

            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                try {
                    if (view != null && request != null) {
                        Log.d(TAG, "shouldOverrideUrlLoading request: " + request.url.toString())
                        //检查是否是重定向或其他需要新加载的情况
                        if (request.isRedirect) {
                            view.loadUrl(request.url.toString())
                            return true
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "shouldOverrideUrlLoading e: ", e)
                }
                return false
            }
        }

        webView.loadUrl(mUrl)

        mBinding?.stateLayout?.let {stateLayout->
            stateLayout.showLoading()
            stateLayout.setErrorRetryClickListener {
                stateLayout.showLoading()
                webView.loadUrl(mUrl)
            }
        }
    }

    override fun initRequestData() {

    }

    override fun onDestroyView() {
        super.onDestroyView()
        mBinding?.webView?.clearHistory()
        mBinding?.webView?.destroy()
    }

    fun goBack():Boolean{
        if(mBinding?.webView?.canGoBack() == true){
            mBinding?.webView?.goBack()
            return true
        }
        return false
    }
}