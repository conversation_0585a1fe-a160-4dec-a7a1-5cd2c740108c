package com.sgmw.ksongs.utils

import androidx.room.TypeConverter

/**
 * @author: 董俊帅
 * @time: 2025/1/22
 * @desc:
 */

class Converters {
    // 将 List<Int> 转换为 String
    @TypeConverter
    fun fromTagIdList(tagIdList: List<Int>?): String {
        return tagIdList?.joinToString(",") ?: ""
    }

    // 将 String 转换为 List<Int>
    @TypeConverter
    fun toTagIdList(tagIdListString: String): List<Int> {
        return tagIdListString.split(",").map { runCatching { it.toInt() }.getOrDefault(0) }
    }
}
