package com.sgmw.ksongs.utils

import android.car.bus.SGMWBus
import android.car.bus.SGMWBusEvent
import android.car.bus.SGMWBusEventType
import android.content.Context
import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import com.sgmw.common.BaseApplication


/**
 * 启动时展示 系统欢迎页面弹框工具类
 */
object LingOsDialogUtils {
    const val TAG = "LingOsDialogUtils"
    const val EVENT_LINGOS_SHOW = "WINDOW/LingOS"
    const val EVENT_CLOSE_WELCOME_PAGE = "launcher/closeWelcomePage"
    const val LINGOS_AGREEMENT = "lingos_agree_agreement"
    const val ZERO = 0
    const val ONE = 1

    private var sgmwBus: SGMWBus? = null
    private var sAgreementListener: IAgreementListener? = null
    private var settingsObserver: SettingsObserver? = SettingsObserver(Handler(Looper.getMainLooper()))

    interface IAgreementListener {
        fun onAgreeState(agree: Boolean)
    }

    fun showPrivacyAgreement(context: Context, listener: IAgreementListener?) {
        val isAgree = Settings.Global.getInt(
            context.contentResolver,
            LINGOS_AGREEMENT,
            LingOsDialogUtils.ZERO
        ) == LingOsDialogUtils.ONE
        Log.d(LingOsDialogUtils.TAG, "showPrivacyAgreement: isAgree = $isAgree")
        if (isAgree) {
            listener?.onAgreeState(true)
            return
        }
        sAgreementListener = listener
        if (sgmwBus == null) {
            sgmwBus = SGMWBus(context)
        }
        subscribeAgreeEvent(context)
        showWelcomeDialog(context)
    }

    //展示弹窗
    private fun showWelcomeDialog(mContext: Context?) {
        val sGMWBus = SGMWBus(mContext)
        val sgmwBusEvent = SGMWBusEvent()
        sgmwBusEvent.mEventType = SGMWBusEventType.EVENT_LINGOS_SHOW
        sGMWBus.publish(sgmwBusEvent)
    }


    private class SettingsObserver(handler: Handler?) : ContentObserver(handler) {
        override fun onChange(selfChange: Boolean, uri: Uri?) {
            super.onChange(selfChange, uri)
            val isAgree = Settings.Global.getInt(
                 BaseApplication.context.contentResolver,
                LINGOS_AGREEMENT,
                ZERO
            ) == ONE
            Log.d(TAG, "SettingsObserver onChange:$isAgree")
            sAgreementListener?.let {
                it.onAgreeState(isAgree)
            }
            if (isAgree) {
                sAgreementListener = null
                sgmwBus = null
                if (settingsObserver != null) {
                    settingsObserver?.let {
                        BaseApplication.context.contentResolver
                            .unregisterContentObserver(it)
                    }
                    settingsObserver = null
                }
            }
        }
    }

    private fun subscribeAgreeEvent(mContext: Context) {
        settingsObserver?.let {
            BaseApplication.context.contentResolver.unregisterContentObserver(it)
            val specificUri = Settings.Global.getUriFor(LINGOS_AGREEMENT)
            mContext.contentResolver.registerContentObserver(
                specificUri,  // 监听的URI
                false,  // 是否监听子URI（通常设为false）
                it
            )
        }

    }
}