package com.sgmw.ksongs.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.util.Log
import com.sgmw.common.BaseApplication
import com.sgmw.common.config.MemoryOptimizationConfig
import kotlinx.coroutines.*

/**
 * 内存监控工具类
 * 用于监控应用内存使用情况，预防OOM异常
 */
object MemoryMonitor {
    
    private const val TAG = "MemoryMonitor"
    // 使用配置文件中的参数
    private val MEMORY_WARNING_THRESHOLD = MemoryOptimizationConfig.MonitorConfig.MEMORY_WARNING_THRESHOLD
    private val MEMORY_CRITICAL_THRESHOLD = MemoryOptimizationConfig.MonitorConfig.MEMORY_CRITICAL_THRESHOLD
    private val MONITOR_INTERVAL = MemoryOptimizationConfig.MonitorConfig.MONITOR_INTERVAL_MS
    
    private var monitorJob: Job? = null
    private var isMonitoring = false
    
    /**
     * 开始内存监控
     */
    fun startMonitoring() {
        if (!MemoryOptimizationConfig.MonitorConfig.ENABLE_MEMORY_MONITORING) {
            Log.d(TAG, "Memory monitoring is disabled in config")
            return
        }

        if (isMonitoring) {
            Log.d(TAG, "Memory monitoring is already running")
            return
        }

        isMonitoring = true
        monitorJob = CoroutineScope(Dispatchers.Default).launch {
            while (isMonitoring) {
                try {
                    checkMemoryUsage()
                    delay(MONITOR_INTERVAL)
                } catch (e: Exception) {
                    Log.e(TAG, "Error during memory monitoring", e)
                }
            }
        }
        Log.d(TAG, "Memory monitoring started")
    }
    
    /**
     * 停止内存监控
     */
    fun stopMonitoring() {
        isMonitoring = false
        monitorJob?.cancel()
        monitorJob = null
        Log.d(TAG, "Memory monitoring stopped")
    }

    /**
     * 检查是否处于内存压力状态
     * 用于异常处理器中判断是否应该进行复杂操作
     */
    fun isMemoryPressureHigh(): Boolean {
        return try {
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()

            // 如果内存使用率超过警告阈值，认为处于内存压力状态
            memoryUsageRatio >= MEMORY_WARNING_THRESHOLD
        } catch (e: Exception) {
            // 如果检查内存状态时出现异常，保守地认为处于内存压力状态
            true
        }
    }
    
    /**
     * 检查内存使用情况
     */
    private fun checkMemoryUsage() {
        try {
            val context = BaseApplication.context
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)
            
            // 获取应用内存使用情况
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()
            
            // 获取详细内存信息
            val memoryInfo2 = Debug.MemoryInfo()
            Debug.getMemoryInfo(memoryInfo2)
            
            Log.d(TAG, "Memory usage: ${(memoryUsageRatio * 100).toInt()}% " +
                    "(${usedMemory / 1024 / 1024}MB / ${maxMemory / 1024 / 1024}MB)")
            Log.d(TAG, "Native heap: ${memoryInfo2.nativePrivateDirty}KB, " +
                    "Dalvik heap: ${memoryInfo2.dalvikPrivateDirty}KB")
            
            when {
                memoryUsageRatio >= MEMORY_CRITICAL_THRESHOLD -> {
                    Log.w(TAG, "Critical memory usage detected: ${(memoryUsageRatio * 100).toInt()}%")
                    performCriticalMemoryCleanup()
                }
                memoryUsageRatio >= MEMORY_WARNING_THRESHOLD -> {
                    Log.w(TAG, "High memory usage detected: ${(memoryUsageRatio * 100).toInt()}%")
                    performMemoryCleanup()
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error checking memory usage", e)
        }
    }
    
    /**
     * 执行内存清理
     */
    private fun performMemoryCleanup() {
        try {
            Log.d(TAG, "Performing memory cleanup")
            
            // 清理图片缓存
            try {
                com.bumptech.glide.Glide.get(BaseApplication.context).clearMemory()
            } catch (e: Exception) {
                Log.e(TAG, "Error clearing Glide memory cache", e)
            }
            
            // 建议垃圾回收
            System.gc()
            
            Log.d(TAG, "Memory cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during memory cleanup", e)
        }
    }
    
    /**
     * 执行关键内存清理
     */
    private fun performCriticalMemoryCleanup() {
        try {
            Log.w(TAG, "Performing critical memory cleanup")
            
            // 清理所有图片缓存
            try {
                val glide = com.bumptech.glide.Glide.get(BaseApplication.context)
                glide.clearMemory()
                // 在后台线程清理磁盘缓存
                CoroutineScope(Dispatchers.IO).launch {
                    glide.clearDiskCache()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error clearing Glide caches", e)
            }
            
            // 同步MMKV数据
            try {
                com.sgmw.common.utils.MMKVUtils.sync()
            } catch (e: Exception) {
                Log.e(TAG, "Error syncing MMKV", e)
            }
            
            // 强制垃圾回收
            System.gc()
            System.runFinalization()
            System.gc()
            
            Log.w(TAG, "Critical memory cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during critical memory cleanup", e)
        }
    }
    
    /**
     * 获取当前内存使用情况
     */
    fun getCurrentMemoryUsage(): MemoryUsageInfo {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat()
        
        return MemoryUsageInfo(
            usedMemoryMB = usedMemory / 1024 / 1024,
            maxMemoryMB = maxMemory / 1024 / 1024,
            usageRatio = memoryUsageRatio
        )
    }
    
    /**
     * 内存使用信息数据类
     */
    data class MemoryUsageInfo(
        val usedMemoryMB: Long,
        val maxMemoryMB: Long,
        val usageRatio: Float
    )
}
