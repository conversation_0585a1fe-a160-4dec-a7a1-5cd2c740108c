package com.sgmw.ksongs.utils
 import android.content.Context
 import android.content.Intent
 import android.util.Log
 import com.sgmw.ksongs.constant.Constant.SCROLL_STATUS
 import com.sgmw.ksongs.constant.Constant.SLIDE_BOTTOM
 import com.sgmw.ksongs.constant.Constant.SLIDE_EDGE_BROADCAST
 import com.sgmw.ksongs.constant.Constant.SLIDE_TOP

object SendBroadCastUtil {
    const val TAG = "SendBroadCastUtil"
    fun sendSlidTopBroadCast(ctx: Context) {
        Log.i(TAG, "scrollstatus:到顶了")
        val intent: Intent = Intent(SLIDE_EDGE_BROADCAST)
        intent.putExtra(SCROLL_STATUS, SLIDE_TOP)
        ctx.sendBroadcast(intent)
    }

    fun sendSlidBottomBroadCast(ctx: Context) {
        Log.i(TAG, "scrollstatus:到底了")
        val intent: Intent = Intent(SLIDE_EDGE_BROADCAST)
        intent.putExtra(SCROLL_STATUS, SLIDE_BOTTOM)
        ctx.sendBroadcast(intent)
    }
}