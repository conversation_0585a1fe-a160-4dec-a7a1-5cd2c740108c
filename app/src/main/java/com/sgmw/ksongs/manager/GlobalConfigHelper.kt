package com.sgmw.ksongs.manager

import android.app.Activity
import android.content.ContentResolver
import android.database.ContentObserver
import android.os.Handler
import android.os.HandlerThread
import android.provider.Settings
import com.sgmw.common.BaseApplication
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.constant.Constant
import java.util.concurrent.CopyOnWriteArrayList

/**
 * @author: 董俊帅
 * @time: 2025/3/21
 * @desc: 系统设置监听
 */
object GlobalConfigHelper {
    private const val TAG = "GlobalConfigHelper"
    private const val SCREEN_WAIT = "standby_settings_status"
    private const val LIMIT_DRIVING = "LIMIT_DRIVING_WATCH_VIDEO"

    private val resolver: ContentResolver = BaseApplication.context.contentResolver
    private val globalConfigChangeList = CopyOnWriteArrayList<GlobalConfigChange>()
    private lateinit var handler: Handler
    private lateinit var mWaiteObserver: ContentObserver

    init {
        initConfigObserver()
    }

    fun registerGlobalConfigChangeListener(change: GlobalConfigChange) {
        if (!globalConfigChangeList.contains(change)) {
            globalConfigChangeList.add(change)
        }
    }

    fun unRegisterGlobalConfigChangeListener(change: GlobalConfigChange) {
        globalConfigChangeList.remove(change)
    }

    private fun initConfigObserver() {
        val thread = HandlerThread("ConfigObserverThread").apply { start() }
        handler = Handler(thread.looper)

        mWaiteObserver = object : ContentObserver(handler) {
            override fun onChange(selfChange: Boolean) {
                val value = getWaitScreenState()
                Log.d(TAG, "mWaiteObserver onChange: $value")
                if (value == 1) {
                    // EventBus.getDefault().post(MediaControlEvent(MediaControlEvent.MediaControlEventCancel))
                }
            }
        }
    }

    /**
     * 调整亮度范围
     */
    private fun adjustBrightnessNumber(brightness: Int): Int {
        return when {
            brightness < 26 -> 26
            brightness > 255 -> 255
            else -> brightness
        }
    }

    /**
     * 关闭自动调节亮度
     */
    fun offAutoBrightness() {
        try {
            if (Settings.System.getInt(resolver, Settings.System.SCREEN_BRIGHTNESS_MODE) ==
                Settings.System.SCREEN_BRIGHTNESS_MODE_AUTOMATIC
            ) {
                Settings.System.putInt(
                    resolver,
                    Settings.System.SCREEN_BRIGHTNESS_MODE,
                    Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL
                )
            }
        } catch (e: Settings.SettingNotFoundException) {
            e.printStackTrace()
        }
    }

    /**
     * 获取系统亮度
     */
    fun getSystemBrightness(): Int {
         return Settings.System.getInt(resolver, Settings.System.SCREEN_BRIGHTNESS, Constant.MAX_BRIGHTNESS)
    }

    fun getWaitScreenState(): Int {
        return Settings.Global.getInt(resolver, SCREEN_WAIT, 0)
    }

    /**
     * 获取行车视频限制开关状态
     *  true  1 --> 开
     *  false 0 --> 关
     */
    fun getLimitDrivingState(): Boolean {
        val limitDrivingWatchVideo = Settings.Global.getInt(resolver, LIMIT_DRIVING, 0)
        return limitDrivingWatchVideo == 1
    }

    /*
     * 设置系统亮度，如果有设置了自动调节，请先调用offAutoBrightness()方法关闭自动调节，否则会设置失败
     */
    fun setSystemBrightness(newBrightness: Int) {
        Log.i(TAG, "setSystemBrightness: $newBrightness")
        Settings.System.putInt(
            resolver, Settings.System.SCREEN_BRIGHTNESS,
            adjustBrightnessNumber(newBrightness)
        )
    }

    //设置当前APP的亮度
    fun setAppBrightness(brightnessPercent: Float, activity: Activity) {
        Log.i(TAG, "setAppBrightness: $brightnessPercent")
        val window = activity.window
        val layoutParams = window.attributes
        layoutParams.screenBrightness = brightnessPercent
        window.attributes = layoutParams
    }

    interface GlobalConfigChange {
        /**
         * 行车限制数据变化
         * @param data 0:关闭 1:开启
         */
        fun limitDrivingDataChange(data: Int)
    }
}