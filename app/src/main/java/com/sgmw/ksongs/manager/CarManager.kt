package com.sgmw.ksongs.manager

import android.car.Car
import android.car.VehiclePropertyIds
import android.car.hardware.CarPropertyValue
import android.car.hardware.power.CarPowerManager
import android.car.hardware.property.CarPropertyManager
import android.car.media.CarAudioManager
import android.car.steeringwheelkey.CarSteeringWheelKeyListener
import android.car.steeringwheelkey.CarSteeringWheelKeyManager
import android.content.ContentResolver
import android.database.ContentObserver
import android.os.Handler
import android.os.HandlerThread
import android.provider.Settings
import android.util.ArrayMap
import android.view.KeyEvent
import androidx.lifecycle.MutableLiveData
import com.sgmw.common.BaseApplication
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.mainLaunch
import com.sgmw.ksongs.application.MyApplication
import com.sgmw.ksongs.constant.Constant
import com.sgmw.ksongs.track.ChannelType
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.utils.AppForegroundUtils
import java.util.concurrent.CopyOnWriteArrayList

/**
 * @author: 董俊帅
 * @time: 2025/3/7
 * @desc: 车机控制及状态监听
 */
object CarManager {

    private const val TAG = "CarManager"

    private var mCar: Car? = null

    /**
     * 行车规制开关配置 TAG
     */
    const val LIMIT_DRIVING_WATCH_VIDEO = "LIMIT_DRIVING_WATCH_VIDEO"

    //待机状态
    private const val SCREEN_WAIT = "standby_settings_status"

    /**
     * 行车规制开关状态  1 表示打开 0表示关闭
     */
    const val DRIVING_VIDEO_LIMIT = 1
    const val DRIVING_VIDEO_NO_LIMIT = 0

    private var mCurrentGearValue = 0
    private var mCurrentLimit = 0
    val mCurrentGear = MutableLiveData<Int>()
    val mCurrentWalkRegulationState = MutableLiveData<Int>()

    //声音监听
    private var mAudioManager: CarAudioManager? = null
    private val mCarVolumeListenerList = CopyOnWriteArrayList<CarVolumeListener>()

    //车控监听--主要用到了档位监听
    private var mPropertyManager: CarPropertyManager? = null

    //上下电状态监听
    private var mPowerManager: CarPowerManager? = null
    private val mCarPowerStateListenerList = CopyOnWriteArrayList<CarPowerStateListener>()
    private var mWalkRegulationState: ContentObserver? = null
    private var mWaiteObserver: ContentObserver? = null
    private var handler: Handler? = null
    private var resolver: ContentResolver? = null

    //车控监听--主要用到了方向盘监听
    private var mSteeringWheelKeyManager: CarSteeringWheelKeyManager? = null
    private var mCarSteeringWheelKeyListener: CarSteeringWheelKeyListener? = null
    private val mKeycodeGestureMap = ArrayMap<Int, Int>().apply {
        put(KeyEvent.KEYCODE_MEDIA_NEXT, CarSteeringWheelKeyManager.KEYEVENT_GESTURE_SINGLE_TAP)
    }
    private var isPowerDownState = false

    /**
     * 注册车控监听
     */
    fun init() {
        if (mCar != null && mCar?.isConnected == true) {
            mCar?.disconnect()
        }
        mCar = Car.createCar(BaseApplication.context)
        initAudio()
        initProperty()
        initPower()
        initObserverWalkRegulationState()
        initSteeringWheelKey()
    }

    fun isPowerDownState():Boolean {
        return this.isPowerDownState
    }
    fun setIsPowerDownState(state:Boolean){
        this.isPowerDownState = state
    }

    /**
     * 检测行车规制开关 状态
     */
    private fun initObserverWalkRegulationState() {
        resolver = BaseApplication.context.contentResolver
        val thread = HandlerThread("ObserverThread")
        thread.start()
        handler = Handler(thread.looper)
        mCurrentLimit = getLimitDrivingState()
        Log.i(TAG, "init : limitDriving = $mCurrentLimit")
        mWalkRegulationState = object : ContentObserver(handler) {
            override fun onChange(selfChange: Boolean) {
                super.onChange(selfChange)
                mCurrentLimit = getLimitDrivingState()
                Log.i(TAG, "onChange: limitDriving = $mCurrentLimit, selfChange = $selfChange")
                mCurrentWalkRegulationState.postValue(mCurrentLimit)
            }
        }
        mWalkRegulationState?.let {
            resolver?.registerContentObserver(
                Settings.Global.getUriFor(LIMIT_DRIVING_WATCH_VIDEO),
                false,
                it
            )
        }

        mWaiteObserver = object : ContentObserver(handler) {
            override fun onChange(selfChange: Boolean) {
                val value: Int = getWaitScreenState()
                Log.i(TAG, "onChange: wait = $value, selfChange = $selfChange")
                if (value == 1 && KaraokePlayerManager.getPlayer().isPlaying) {
                    KaraokePlayerManager.getPlayer().pause()
                }
            }
        }
        mWaiteObserver?.let {
            resolver?.registerContentObserver(
                Settings.Global.getUriFor(SCREEN_WAIT),
                false,
                it
            )
        }
    }

    /**
     * 解注册行车规制状态监听
     */
    fun unRegisterContentObserver() {
        mWalkRegulationState?.let {
            resolver?.unregisterContentObserver(it)
        }
        mWaiteObserver?.let {
            resolver?.unregisterContentObserver(it)
        }
    }

    /**
     * 获取待机状态
     * @return 0是隐藏,1是显示
     */
    fun getWaitScreenState(): Int {
        return Settings.Global.getInt(resolver, SCREEN_WAIT, 0)
    }

    private fun initAudio() {
        mAudioManager = mCar!!.getCarManager(Car.AUDIO_SERVICE) as CarAudioManager
        mAudioManager?.registerCarVolumeCallback(object : CarAudioManager.CarVolumeCallback() {
            override fun onGroupVolumeChanged(zoneId: Int, groupId: Int, flags: Int) {
                Log.d(TAG, "onGroupVolumeChanged zoneId = $zoneId, groupId = $groupId, flags = $flags")
                for (listener in mCarVolumeListenerList) {
                    listener.onGroupVolumeChanged(zoneId, groupId, flags)
                }
            }
        })
    }

    fun registerAudioListener(listener: CarVolumeListener) {
        if (!mCarVolumeListenerList.contains(listener)) {
            mCarVolumeListenerList.add(listener)
        }
    }

    /**
     * 设置音量
     * @param volume 0~39
     */
    fun setKaraokeVoiceVolume(volume: Int) {
        Log.d(TAG, "setKaraokeVoiceVolume volume = $volume")
        if (volume < 0 || volume > 39) return
        mAudioManager?.setGroupVolume(
            CarAudioManager.VOLUME_GROUP_KARAOKE_VOICE,
            volume,
            0
        )
    }

    /**
     * 信号 目前只监听挡位
     */
    private val PROPERTY_ID_LIST = intArrayOf(
        VehiclePropertyIds.VEHICLE_ACTUAL_GEAR_STATUS
    )

    private val carPropertyEventCallback = object : CarPropertyManager.CarPropertyEventCallback {
        override fun onChangeEvent(carPropertyValue: CarPropertyValue<*>?) {
            try {
                carPropertyValue?.let { carProperty ->
                    when (carProperty.propertyId) {

                        VehiclePropertyIds.VEHICLE_ACTUAL_GEAR_STATUS -> {
                            //TODO 挂挡监听
                            val currentGearValue = carPropertyValue.value as Int
                            mCurrentGearValue = currentGearValue
                            mCurrentGear.postValue(mCurrentGearValue)
                            //    currentGearValue	整车当前档位状态
                            //    $0=Initialize(初始化)
                            //    $6=error( 故障)
                            //    $a=Park gear(P档)
                            //    $b=Sport Gear(S档)
                            //    $c=Drive Gear(D档)
                            //    $d=Neutral Gear(空档)
                            //    $e=Reverse Gear(倒档)
                            Log.d(TAG, " currentGearValue: $currentGearValue")
                        }

                        else -> {}
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "initProperty onChangeEvent error = ${e.message}")
            }
            Log.d(TAG, "onChangeEvent carPropertyValue = $carPropertyValue")
        }

        override fun onErrorEvent(p0: Int, p1: Int) {
            Log.e(TAG, "initProperty onErrorEvent p0 = $p0, p1 = $p1")
        }

    }

    private fun initProperty() {
        mPropertyManager = mCar!!.getCarManager(Car.PROPERTY_SERVICE) as CarPropertyManager
        // 连接之后 获取到当前挡位
        mCurrentGearValue = mPropertyManager?.getIntProperty(VehiclePropertyIds.VEHICLE_ACTUAL_GEAR_STATUS, 0) ?: 0
        // 发出通知当前挡位
        mCurrentGear.postValue(mCurrentGearValue)
        Log.d(TAG, "initProperty intProperty  挡位 = $mCurrentGearValue ")
        // 监听挡位变化
        PROPERTY_ID_LIST.forEach { propertyId ->
            mPropertyManager?.registerCallback(carPropertyEventCallback, propertyId, 0.0F)
        }
    }

    // 当前是否是P档 prd:3.8.3 行车视频限制
    fun isParkGear(): Boolean {
        Log.d(TAG, "isParkGear $mCurrentGearValue")
        if (mCurrentGearValue == Constant.GEAR_PARK) {
            return false
        }
        return true
    }

    /**
     * 行车限制弹窗显示判断条件
     *
     * @return showLimit
     */
    fun isLimit(): Boolean {
        return mCurrentLimit == 1 && mCurrentGearValue != 0x0A && mCurrentGearValue != 0x00
    }

    private val powerStateListener: CarPowerManager.CarPowerStateListener =
        object : CarPowerManager.CarPowerStateListener {
            override fun onStateChanged(state: Int) {
                Log.d(TAG, "onStateChanged state = $state")
                when (state) {
                    CarPowerManager.STATE_ON -> {
                        // 上电
                    }

                    CarPowerManager.STATE_SHUTDOWN_PREPARE,
                    CarPowerManager.STATE_STANDBY -> {
                        // 下电 调用stop 播放状态会变更 4 END状态
                        KaraokePlayerManager.pause()
                        setIsPowerDownState(true)
                        mainLaunch {
                            try {
                                Log.d(TAG, "onStateChanged mainActivity finish")
                                MyApplication.mainActivity.finish()
                            } catch (e: Exception) {
                                Log.e(TAG, "onStateChanged mainActivity finish error = ${e.message}")
                            }
                        }
                    }
                }
                for (listener in mCarPowerStateListenerList) {
                    listener.onStateChanged(state)
                }
            }
        }

    private fun initPower() {
        mPowerManager = mCar!!.getCarManager(Car.POWER_SERVICE) as CarPowerManager
        mPowerManager?.setListener(powerStateListener)
    }

    fun registerCarPowerStateListener(listener: CarPowerStateListener) {
        if (!mCarPowerStateListenerList.contains(listener)) {
            mCarPowerStateListenerList.add(listener)
        }
    }

    fun unregisterCarPowerStateListener(listener: CarPowerStateListener) {
        mCarPowerStateListenerList.remove(listener)
    }

    fun clearAllListeners() {
        mCarVolumeListenerList.clear()
        mCarPowerStateListenerList.clear()
    }

    /**
     * 注册方向盘监听
     */
    private fun initSteeringWheelKey() {
        mSteeringWheelKeyManager = mCar!!.getCarManager(Car.STEERING_WHEEL_SERVICE) as CarSteeringWheelKeyManager
        mCarSteeringWheelKeyListener = object : CarSteeringWheelKeyListener {
            override fun onLongPress(event: KeyEvent) {
                Log.d(TAG, "onLongPress, event=$event")
            }

            override fun onSingleTap(event: KeyEvent) {
                Log.d(TAG, "onSingleTap, event=$event")
                KaraokePlayerManager.playNextSong(channelType = ChannelType.CONTROL_CLICK)
            }
        }
        mSteeringWheelKeyManager?.setListener(
            BaseApplication.context,
            CarSteeringWheelKeyManager.MODE_MEDIA,
            mKeycodeGestureMap,
            mCarSteeringWheelKeyListener
        )
    }

    /**
     * 申请方向盘监控焦点
     */
    fun requestWheelKeyMode() {
        val requestMode =
            mSteeringWheelKeyManager?.requestMode(BaseApplication.context, CarSteeringWheelKeyManager.MODE_MEDIA)
        Log.d(TAG, "requestWheelKeyMode requestMode = $requestMode")
    }

    /**
     * 释放方向盘监控焦点
     */
    fun exitWheelKeyMode() {
        val exitMode =
            mSteeringWheelKeyManager?.exitMode(BaseApplication.context, CarSteeringWheelKeyManager.MODE_MEDIA)
        Log.d(TAG, "exitWheelKeyMode exitMode = $exitMode")
    }


    /**
     * 获取行车限制配置
     * @return 0是关闭,1是开启
     */
    fun getLimitDrivingState(): Int {
        return Settings.Global.getInt(resolver, LIMIT_DRIVING_WATCH_VIDEO, 1)
    }

    /**
     * 0x00=Initialize（初始化）;
     * 0x01=UnusedandReserved（预留）;
     * 0x02=UnusedandReserved（预留）;
     * 0x03=UnusedandReserved（预留）;
     * 0x04=UnusedandReserved（预留）;
     * 0x05=UnusedandReserved（预留）;
     * 0x06=error（故障）;
     * 0x07=UnusedandReserved（预留）;
     * 0x08=UnusedandReserved（预留）;
     * 0x0A=Parkgear（P档）;
     * 0x0B=SportGear（S档）;
     * 0x0C=DriveGear（D档）;
     * 0x0D=NeutralGear（空档）;
     * 0x0E=ReverseGear（倒档）;
     * 0x0F=UnusedandReserved（预留）;
     */
    fun getVehicleGear(): Int {
        return mCurrentGearValue
    }

    interface CarVolumeListener {
        fun onGroupVolumeChanged(zoneId: Int, groupId: Int, flags: Int)
    }

    interface PropertyListener {
        fun onGearChange(gear: Int)
    }

    interface CarPowerStateListener {
        fun onStateChanged(state: Int)
    }
}