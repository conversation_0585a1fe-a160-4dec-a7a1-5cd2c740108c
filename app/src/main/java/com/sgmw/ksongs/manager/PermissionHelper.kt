package com.sgmw.ksongs.manager

import android.Manifest
import android.text.TextUtils
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.GsonUtils
import com.google.gson.reflect.TypeToken
import com.sgmw.common.BaseApplication
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.ioLaunch
import com.sgmw.common.utils.mainLaunch
import com.sgmw.ksongs.BuildConfig
import com.sgmw.ksongs.MainActivity
import com.sgmw.ksongs.application.MyApplication
import com.sgmw.ksongs.ui.dialog.PermissionDialogFragment
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.viewmodel.MainViewModel
import com.sgmw.permissionsdk.PermissionChangeListener
import com.sgmw.permissionsdk.PermissionManager
import com.sgmw.permissionsdk.bean.PermissionApp
import com.tme.ktv.video.api.VideoState

/**
 * @author: 董俊帅
 * @time: 2025/4/8
 * @desc: 权限管理类
 */
object PermissionHelper {

    private const val TAG = "PermissionHelper"

    private var localPermissionState = false

    /**
     * 检查录音权限
     */
    fun checkPermission(isAutoPlayNext: Boolean = false, callBack: (Boolean) -> Unit) {
        if (BuildConfig.IS_PHONE) {
            callBack(true)
            return
        }

        // 使用IO线程处理权限检查，避免阻塞主线程
        ioLaunch {
            try {
                // 如果正在K歌状态，则直接返回当前麦克风的状态，不需要重新弹框
                val playState = KaraokePlayerManager.getPlayer().playState
                if (playState == VideoState.STATE_PLAYING || playState == VideoState.STATE_PAUSE || isAutoPlayNext) {
                    val hasPermission = isHaveMicrophonePermission()
                    mainLaunch {
                        callBack(hasPermission)
                    }
                    return@ioLaunch
                }

                val hasPermission = isHaveMicrophonePermission()
                if (hasPermission) {
                    Log.d(TAG, "Permission already granted")
                    localPermissionState = true
                    mainLaunch {
                        callBack(true)
                    }
                    return@ioLaunch
                }

                // 需要显示权限对话框时，切换到主线程
                mainLaunch {
                    showPermissionDialog {
                        callBack.invoke(it)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "checkPermission error: ${e.message}", e)
                mainLaunch {
                    callBack(false)
                }
            }
        }
    }

    /**
     * 是否有用麦克风权限
     */
    fun isHaveMicrophonePermission(): Boolean {
        return if (BuildConfig.IS_PHONE) {
            true
        } else {
            PermissionManager.getInstance()
                .checkPermission(BaseApplication.application.packageName, Manifest.permission_group.MICROPHONE)
        }
    }

    /**
     * 权限动态监听
     */
    fun registerChangeListener(activity: MainActivity) {
        if (BuildConfig.IS_PHONE) {
            return
        } else {
            PermissionManager.getInstance().registerChangeListener(object : PermissionChangeListener {
                override fun permissionChanges(data: String?) {
                    try {
                        val permissionApps: List<PermissionApp> =
                            GsonUtils.fromJson(data, object : TypeToken<List<PermissionApp?>?>() {
                            }.type)
                        for (app in permissionApps) {
                            if (TextUtils.equals(app.packageName, BaseApplication.application.packageName)) {
                                for (groups in app.permissionGroups) {
                                    if (TextUtils.equals(groups.groupName, Manifest.permission_group.MICROPHONE)) {
                                        //此处fwk反馈只能用flags判断权限
                                        if (!groups.isGrant && localPermissionState) {
                                            ViewModelProvider(activity)[MainViewModel::class.java].isPermissionChange.postValue(
                                                true
                                            )
                                        }
                                        localPermissionState = groups.isGrant
                                        return
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "permissionChanges " + e.message)
                    }
                }

            })
        }
    }

    /**
     * 设置调音弹框
     */
    fun showPermissionDialog(listener: (Boolean) -> Unit) {
        val permissionDialogFragment = PermissionDialogFragment({ isGrant ->
            listener.invoke(isGrant)
        })
        // 检查 OpenScoreDialog 是否已经显示
        val fragment = MyApplication.mainActivity.supportFragmentManager.findFragmentByTag("PermissionDialogFragment")
        if (fragment == null) {
            // 弹出 PermissionDialogFragment
            permissionDialogFragment.show(MyApplication.mainActivity.supportFragmentManager, "PermissionDialogFragment")
        }
    }

}