package com.sgmw.ksongs.application


import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import com.autoai.baseline.support.autoinflater.AutoInflaterManager
import com.autoai.baseline.support.skincore.SkinManager
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import com.sgmw.common.BaseApplication
import com.sgmw.common.config.MemoryOptimizationConfig
import com.sgmw.common.utils.DaemonThreadDispatcher
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.MMKVUtils
import com.sgmw.ksongs.BuildConfig
import com.sgmw.ksongs.MainActivity
import com.sgmw.ksongs.db.DbManager
import com.sgmw.ksongs.manager.ANRMonitor
import com.sgmw.ksongs.manager.AudioFocusHelper
import com.sgmw.ksongs.manager.CarManager
import com.sgmw.ksongs.model.repository.AccessTokenManager
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.utils.AppForegroundUtils
import com.sgmw.ksongs.utils.MemoryMonitor
import com.sgmw.ksongs.utils.sdk_init.GlideImageLoaderImpl
import com.sgmw.ksongs.utils.sdk_init.SampleAppRequestProvider
import com.sgmw.ksongs.utils.sdk_init.SampleHostInfo
import com.sgmw.ksongs.utils.sdk_init.SimpleLog
import com.sgmw.ksongs.viewmodel.settings.SettingsViewModel
import com.sgmw.ksongs.vr.VrKSongsImpl
import com.sgmw.ksongs.widget.MyRefreshFooter
import com.sgmw.voice_engine.manager.adapter.VrKSongManager
import com.sgmw.voice_engine.manager.sdk.SdkManager
import com.tencent.karaoke.download.cache.AbstractCacheStrategy
import com.tencent.karaoke.download.cache.CacheStrategyFactory
import com.tencent.karaoke.download.cache.LfuCacheStrategy
import com.tme.ktv.api.KtvInitParams
import com.tme.ktv.api.KtvSdk
import com.tme.ktv.common.util.KServiceManager
import com.tme.ktv.login.api.IKGetToken
import com.tme.ktv.login.api.ThirdPartyLoginService
import com.tme.ktv.login.api.ThirdPartyLoginToken
import com.tme.ktv.room.IKGPush
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import ksong.support.player.KtvPlayerConfig


class MyApplication : BaseApplication() {

    companion object {
        private const val TAG = "MyApplication"

        @SuppressLint("StaticFieldLeak")
        lateinit var mainActivity: MainActivity
    }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        Log.d(TAG, "attachBaseContext")
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "onCreate -- ")

        try {
            // 基础组件初始化
            MMKVUtils.initMMKV(this)
            // 初始化应用前后台状态监听
            AppForegroundUtils.init(this)

            // 核心功能初始化
            //初始化数据库
            DbManager.initDb(this)
            AudioFocusHelper.init(this)

            // 第三方SDK初始化 - 使用applicationContext避免内存泄漏
            initKG()
            SensorsDataManager.initSensorsDataSDK(applicationContext)

            // 初始化换肤 - 延迟初始化以减少启动时内存压力
            initSkin()

            SmartRefreshLayout.setDefaultRefreshFooterCreator { _, _ -> MyRefreshFooter(context) }

            if (!BuildConfig.IS_PHONE) {
                CarManager.init()
            }

            // 启动内存监控
            MemoryMonitor.startMonitoring()

            // 启动ANR监控
            ANRMonitor.startMonitoring()

            // 使用守护线程进行应用初始化，避免阻塞应用关闭
            CoroutineScope(DaemonThreadDispatcher.Default).launch {
                try {
                    // 修复内置app 无法使用webView的问题
                    hookWebView()
                    // 语音识别初始化 - 使用applicationContext
                    SdkManager.getInstance().init(applicationContext)
                    VrKSongManager.getInstance().setIKSongListener(VrKSongsImpl())
                } catch (e: Exception) {
                    Log.e(TAG, "Application initialization failed", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error during application initialization", e)
        }
    }

    private fun initKG() {
        val params = KtvInitParams(
            hostInfo = SampleHostInfo(),
            appRequestProvider = SampleAppRequestProvider(),
            imageLoader = GlideImageLoaderImpl(),
            logger = SimpleLog(),
        )
        KtvSdk.init(this, params)
        KtvSdk.setProductEnv(false)
        customConfig()

        initOptionalServices(params)

        ThirdPartyLoginService.setLoginTokenImpl(object : IKGetToken {
            override fun onGetToken(): ThirdPartyLoginToken {
                Log.d(
                    TAG,
                    "onGetToken AccessTokenManager.isLogin(): ${AccessTokenManager.isLogin()}"
                )
                return if (AccessTokenManager.isLogin()) {
                    //已登录，由接入方获取当前最新token（注意token有时效性，需要接入方保证token是最新的）
                    runBlocking {
                        Log.d(
                            TAG,
                            "AccessTokenManager.getAccessToken()--> ${AccessTokenManager.getAccessToken()}" +
                                    "AccessTokenManager.getAccessTokenBean()!!.openid---> ${AccessTokenManager.getAccessTokenBean()!!.openid}"
                        )
                        ThirdPartyLoginToken(
                            true,
                            AccessTokenManager.getAccessToken(),
                            AccessTokenManager.getAccessTokenBean()!!.openid
                        )
                    }
                } else {
                    //未登录时返回false，未登录状态需厂商自己判断
                    ThirdPartyLoginToken(false, null, null)
                }
            }
        })

    }

    /**
     * K歌的一些可选服务，需要使用时才需要初始化。
     */
    private fun initOptionalServices(params: KtvInitParams) {
        KServiceManager.getService(IKGPush::class.java)?.initPush(this, params.hostInfo.uid)
    }

    private fun customConfig() {
        val audioDeviceInfo = AudioFocusHelper.getAudioDeviceInfo()
        Log.d(TAG, "audioDeviceInfo address = ${audioDeviceInfo?.address}")
        KtvPlayerConfig.getInstance()
            .setCacheStrategyFactory(object : CacheStrategyFactory() {
                override fun createCacheStrategy(cacheDirPath: String): AbstractCacheStrategy {
                    Log.d(TAG, "cacheDirPath = $cacheDirPath")
                    SettingsViewModel.cacheDirPath = cacheDirPath
                    // 使用配置文件中的缓存大小
                    val cacheSize = MemoryOptimizationConfig.CacheConfig.KTV_CACHE_SIZE_GB * 1024 * 1024 * 1024L
                    return LfuCacheStrategy(cacheDirPath, cacheSize)
                }
            })
            .setHttpReadTimeoutMs(10 * 1000)
            .setHttpConnectTimeoutMs(10 * 1000)
            .useOpenGL(true)
            .setPreferredDevice(AudioFocusHelper.getAudioDeviceInfo()) //设置录音PreferredDevice，适配底软算法
            .commit()   //此方法必须被调用
    }

    /**
     * 初始化换肤
     */
    private fun initSkin(): String {
        Log.d(TAG, "initSkin")
        AutoInflaterManager.getInstance() //可选：日志开关，默认关闭日志
            .setLoggable(false) //可选，可以用于配置自定义LayoutFactory
            .setSoundEffectsEnabled(true)
            //  .setLayoutFactory(MediaSkinSdkLayoutFactory()) //添加插件：换肤框架看做 AutoInflater 框架的扩展插件
            .addPlugin(
                SkinManager.getInstance() //可选：日志开关，默认关闭日志
                    .setLoggable(false) //默认为 true：换肤配置是否和系统保持同步
                    .setEnTextSizeDif(4f, 28, true)
                    .setSystemSync(true) //默认为false：是否支持 text、hint 等字符串显示内容的 更换，如果是多语言应用可以开启，用于多语言变更控制
                    .setSupportTextStr(true) //默认为false，是否忽略前后台;为false，切换皮肤或者昼夜模式后应用在前台的时候才开始生效；为true，则切换后不管应用在前台还是后台，立即生效。用于优化性能的配置选项，避免系统中所有App同时集中生效。
                    //.setIgnoredForeground(true)//可选：（慎用， 增加性能消耗）默认为false，是否忽略前后台; false：应用在前台的时候换肤才开始生效；true：不区分前后台立即生效。用于优化性能的配置选项，避免系统中所有App同时集中生效。
                    //可选：云端文言功能配置，如果App需要支持云端文言，请配置此项
                    .initPlugin()
            )
            .init(this)
        return "initSkin->"
    }

    override fun onTerminate() {
        super.onTerminate()
        // 停止内存监控
        MemoryMonitor.stopMonitoring()

        // 停止ANR监控
        ANRMonitor.stopMonitoring()
        performCleanup("onTerminate")
    }

    /**
     * 执行应用清理操作
     * 注意：onTerminate在实际设备上可能不会被调用，主要清理应该在Activity的onDestroy中进行
     */
    private fun performCleanup(source: String) {
        try {
            Log.d(TAG, "Starting application cleanup from: $source")

            // 清理车机管理器
            CarManager.unRegisterContentObserver()

            // 清理守护线程调度器（放在最后）
            DaemonThreadDispatcher.shutdown()

            Log.d(TAG, "Application cleanup completed from: $source")
        } catch (e: Exception) {
            Log.e(TAG, "Error during application cleanup from: $source", e)
        }
    }

    /**
     * 修改系统对webView限制
     */
    @SuppressLint("SoonBlockedPrivateApi")
    private fun hookWebView() {
        val sdkInt = Build.VERSION.SDK_INT
        try {
            val factoryClass = Class.forName("android.webkit.WebViewFactory")
            val field = factoryClass.getDeclaredField("sProviderInstance")
            field.isAccessible = true
            var sProviderInstance = field[null]
            if (sProviderInstance != null) {
                Log.i(TAG, "sProviderInstance isn't null")
                return
            }
            val getProviderClassMethod = if (sdkInt > 22) {
                factoryClass.getDeclaredMethod("getProviderClass")
            } else if (sdkInt == 22) {
                factoryClass.getDeclaredMethod("getFactoryClass")
            } else {
                Log.i(TAG, "Don't need to Hook WebView")
                return
            }
            getProviderClassMethod.isAccessible = true
            val factoryProviderClass = getProviderClassMethod.invoke(factoryClass) as Class<*>
            val delegateClass = Class.forName("android.webkit.WebViewDelegate")
            val delegateConstructor = delegateClass.getDeclaredConstructor()
            delegateConstructor.isAccessible = true
            if (sdkInt < 26) { //低于Android O版本
                val providerConstructor = factoryProviderClass.getConstructor(delegateClass)
                if (providerConstructor != null) {
                    providerConstructor.isAccessible = true
                    sProviderInstance = providerConstructor.newInstance(delegateConstructor.newInstance())
                }
            } else {
                val chromiumMethodName = factoryClass.getDeclaredField("CHROMIUM_WEBVIEW_FACTORY_METHOD")
                chromiumMethodName.isAccessible = true
                var chromiumMethodNameStr = chromiumMethodName[null] as String
                if (chromiumMethodNameStr == null) {
                    chromiumMethodNameStr = "create"
                }
                val staticFactory = factoryProviderClass.getMethod(chromiumMethodNameStr, delegateClass)
                if (staticFactory != null) {
                    sProviderInstance = staticFactory.invoke(null, delegateConstructor.newInstance())
                }
            }

            if (sProviderInstance != null) {
                field["sProviderInstance"] = sProviderInstance
                Log.i(TAG, "Hook success!")
            } else {
                Log.i(TAG, "Hook failed!")
            }
        } catch (e: Throwable) {
            Log.w(TAG, e)
        }
    }

}