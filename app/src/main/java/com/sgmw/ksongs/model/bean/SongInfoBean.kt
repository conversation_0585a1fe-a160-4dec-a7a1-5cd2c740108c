package com.sgmw.ksongs.model.bean

import android.os.Parcelable
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Parcelize
data class SongInfoBean(
    var `1080_mv_cover_size`: Int = 0,
    var `1080_mv_size`: Int = 0,
    var `480_mv_cover_size`: Int = 0,
    var `480_mv_size`: Int = 0,
    var `720_mv_cover_size`: Int = 0,
    var `720_mv_size`: Int = 0,
    var album_img: String = "",
    var cp_status: Int = 0,
    var has_hq: Boolean = false,
    var has_lyric: Int = 0,
    var has_midi: Boolean = false,
    var has_mv: Boolean = false,
    var has_ori_song: Boolean = false,
    var language: Int = 0,
    var mv_cover: String = "",
    var mv_has_lyric: Boolean = false,
    var mv_height: Int = 0,
    var mv_width: Int = 0,
    var need_vip: Boolean = false,
    var play_count: Int = 0,
    var play_duration: Int = 0,
    var qqmusic_id: Int = 0,
    var singer_id: String = "",
    var singer_name: String = "",
    var song_desc: String = "",
    var song_id: String = "",
    var song_lyric_mask: Int = 0,
    var song_name: String = "",
    var song_type: Int = 0,
    var status: Int = 0,
    var tag_id_list: List<Int> = listOf()
): Parcelable {
    /**
     * 是否是已收藏
     */
    @IgnoredOnParcel
    var isCollect: Boolean = false
    /**
     * 是否在已点列表中
     */
    @IgnoredOnParcel
    var isInDemandList = false

    var isPlaying = false
    // 歌曲是否正在播放状态
    @IgnoredOnParcel
    var isPlayingState = false
    override fun hashCode(): Int {
        return super.hashCode()
    }

    // 自定义copy方法以处理null值
    fun deepCopy(
        `1080_mv_cover_size`: Int = this.`1080_mv_cover_size`,
        `1080_mv_size`: Int = this.`1080_mv_size`,
        `480_mv_cover_size`: Int = this.`480_mv_cover_size`,
        `480_mv_size`: Int = this.`480_mv_size`,
        `720_mv_cover_size`: Int = this.`720_mv_cover_size`,
        `720_mv_size`: Int = this.`720_mv_size`,
        album_img: String = this.album_img,
        cp_status: Int = this.cp_status,
        has_hq: Boolean = this.has_hq,
        has_lyric: Int = this.has_lyric,
        has_midi: Boolean = this.has_midi,
        has_mv: Boolean = this.has_mv,
        has_ori_song: Boolean = this.has_ori_song,
        language: Int = this.language,
        mv_cover: String = this.mv_cover,
        mv_has_lyric: Boolean = this.mv_has_lyric,
        mv_height: Int = this.mv_height,
        mv_width: Int = this.mv_width,
        need_vip: Boolean = this.need_vip,
        play_count: Int = this.play_count,
        play_duration: Int = this.play_duration,
        qqmusic_id: Int = this.qqmusic_id,
        singer_id: String = this.singer_id,
        singer_name: String = this.singer_name,
        song_desc: String = this.song_desc,
        song_id: String = this.song_id,
        song_lyric_mask: Int = this.song_lyric_mask,
        song_name: String = this.song_name,
        song_type: Int = this.song_type,
        status: Int = this.status,
        tag_id_list: List<Int>? = this.tag_id_list,
        isCollect: Boolean = this.isCollect,
        isInDemandList: Boolean = this.isInDemandList,
        isPlaying:Boolean = this.isPlaying,
        isPlayingState:Boolean = this.isPlayingState
    ): SongInfoBean {
        val info = SongInfoBean(
            `1080_mv_cover_size` = `1080_mv_cover_size`,
            `1080_mv_size` = `1080_mv_size`,
            `480_mv_cover_size` = `480_mv_cover_size`,
            `480_mv_size` = `480_mv_size`,
            `720_mv_cover_size` = `720_mv_cover_size`,
            `720_mv_size` = `720_mv_size`,
            album_img = album_img,
            cp_status = cp_status,
            has_hq = has_hq,
            has_lyric = has_lyric,
            has_midi = has_midi,
            has_mv = has_mv,
            has_ori_song = has_ori_song,
            language = language,
            mv_cover = mv_cover,
            mv_has_lyric = mv_has_lyric,
            mv_height = mv_height,
            mv_width = mv_width,
            need_vip = need_vip,
            play_count = play_count,
            play_duration = play_duration,
            qqmusic_id = qqmusic_id,
            singer_id = singer_id,
            singer_name = singer_name,
            song_desc = song_desc,
            song_id = song_id,
            song_lyric_mask = song_lyric_mask,
            song_name = song_name,
            song_type = song_type,
            status = status,
            tag_id_list = tag_id_list ?: listOf()
        )
        info.isCollect = isCollect
        info.isInDemandList = isInDemandList
        info.isPlaying = isPlaying
        info.isPlayingState = isPlayingState
        return  info
    }
}