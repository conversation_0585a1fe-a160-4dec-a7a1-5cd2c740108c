package com.sgmw.ksongs.db

import android.content.Context
import androidx.room.Room.databaseBuilder
import com.sgmw.common.utils.DaemonThreadDispatcher
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.db.database.KSongsDatabase
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.utils.MemoryMonitor
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

object DbManager {

    private const val TAG = "DbManager"

    private lateinit var KSongsDb: KSongsDatabase
    private var isDbClosed = false
    private lateinit var appContext: Context

    // 数据库专用异常处理器
    private val dbExceptionHandler = CoroutineExceptionHandler { _, throwable ->
        try {
            // 检查内存压力，避免在内存不足时进行复杂操作
            if (MemoryMonitor.isMemoryPressureHigh()) {
                android.util.Log.e(TAG, "Database exception under memory pressure: ${throwable.javaClass.simpleName}")
            } else {
                Log.e(TAG, "Database operation exception", throwable)
            }
        } catch (e: Exception) {
            // 异常处理器本身出现异常时的最后防线
            android.util.Log.e(TAG, "Exception in DB exception handler: $e, original: $throwable")
        }
    }

    // 使用守护线程的数据库作用域，避免阻塞应用关闭，添加异常处理器
    private var dbScope = CoroutineScope(SupervisorJob() + DaemonThreadDispatcher.Database + dbExceptionHandler)

    /**
     * 初始化数据库 - 使用守护线程避免阻塞应用关闭
     */
    fun initDb(context: Context) {
        appContext = context.applicationContext
        isDbClosed = false

        dbScope.launch {
            try {
                KSongsDb = databaseBuilder(appContext, KSongsDatabase::class.java, "KSongs")
                    .build()

                // 安全地获取播放中的歌曲信息
                try {
                    KaraokeConsole.currSongInfo = getDemandSongInfoDao().getPlayingSongInfo()?.songInfo
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to get playing song info during initialization", e)
                    KaraokeConsole.currSongInfo = null
                }

                Log.d(TAG, "Database initialized successfully")
            } catch (e: Exception) {
                // 数据库初始化异常处理，避免崩溃
                Log.e(TAG, "Database initialization failed", e)
            }
        }
    }

    fun getDemandSongInfoDao() = KSongsDb.getDemandSongInfoDao()

    /**
     * 获取收藏数据库操作dao
     */
    fun getCollectDao() = KSongsDb.getCollectDao()

    /**
     * 播放记录Dao
     */
    fun getPlayRecordDao() = KSongsDb.getPlayRecordDao()

}