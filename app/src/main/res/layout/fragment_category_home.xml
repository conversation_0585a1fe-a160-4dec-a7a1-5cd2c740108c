<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_100"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginStart="@dimen/dp_72"
        android:layout_marginEnd="@dimen/dp_222"
        app:tabGravity="start"
        app:tabIndicatorAnimationDuration="160"
        app:tabMaxWidth="@dimen/dp_500"
        app:tabMode="scrollable"
        app:tabPaddingTop="@dimen/dp_14"
        android:paddingBottom="@dimen/dp_24"
        app:tabPaddingEnd="@dimen/dp_80"
        app:tabPaddingStart="@dimen/dp_80"
        app:tabRippleColor="@null"
        app:tabIndicatorFullWidth="false"
        app:tabIndicator="@drawable/tab_indicator"
        app:tabIndicatorColor="@color/c_1990FF"
        app:tabSelectedTextColor="@color/tab_text_color_selected"
        app:tabTextAppearance="@style/TabLayoutTextStyle"
        app:tabTextColor="@color/tab_text_color_normal" />

    <View
        android:layout_width="@dimen/dp_152"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tabLayout"
        app:layout_constraintBottom_toBottomOf="@id/tabLayout"
        android:background="@color/bg_main_color"/>

    <View
        android:layout_width="@dimen/dp_302"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tabLayout"
        app:layout_constraintBottom_toBottomOf="@id/tabLayout"
        android:background="@color/bg_main_color"/>

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:layout_marginStart="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_22"
        android:src="@drawable/selector_icon_56_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_80"
        android:layout_marginEnd="@dimen/dp_190"
        android:layout_marginTop="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@mipmap/logo_ksong" />

    <TextView
        android:id="@+id/tvLogoInfo"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_48"
        android:lineSpacingExtra="@dimen/dp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginEnd="@dimen/dp_64_5"
        android:layout_marginTop="@dimen/dp_24"
        android:text="@string/app_name"
        android:textSize="@dimen/sp_32"
        android:textColor="@color/right_logo_txt_color" />

    <com.sgmw.common.widget.StateLayout
        android:id="@+id/stateLayout"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:overScrollMode="never"
        app:loadingIvTopMargin="@dimen/dp_247"
        app:errorIvTopMargin="@dimen/dp_166"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />

    </com.sgmw.common.widget.StateLayout>


</androidx.constraintlayout.widget.ConstraintLayout>