<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_main_color">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_80"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_190"
        android:importantForAccessibility="no"
        android:src="@mipmap/logo_ksong"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_64_5"
        android:text="@string/app_name"
        android:textColor="@color/right_logo_txt_color"
        android:textSize="@dimen/sp_32"
        android:contentDescription="(@hide_uc(value=[true]))"
        app:layout_constraintBottom_toBottomOf="@+id/iv_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_icon" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_156"
        android:fontFamily="sans-serif-medium"
        android:text="@string/privacy_policy_title"
        android:contentDescription="(@hide_uc(value=[true]))"
        android:textColor="@color/normal_text_color"
        android:textSize="@dimen/sp_32"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_icon" />

    <TextView
        android:id="@+id/tv_content1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_80"
        android:text="@string/privacy_policy_content1"
        android:textColor="@color/right_logo_txt_color"
        android:textSize="@dimen/sp_28"
        android:contentDescription="(@hide_uc(value=[true]))"
        app:layout_constraintStart_toStartOf="@+id/tv_content2"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <TextView
        android:id="@+id/tv_content2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_285"
        android:layout_marginTop="@dimen/dp_10"
        android:text="@string/privacy_policy_content2"
        android:textColor="@color/normal_text_color"
        android:textSize="@dimen/sp_28"
        android:contentDescription="(@hide_uc(value=[true]))"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_content1" />

    <TextView
        android:id="@+id/bt_agree"
        android:layout_width="@dimen/dp_180"
        android:layout_height="@dimen/dp_72"
        android:layout_marginTop="@dimen/dp_80"
        android:background="@drawable/selector_btn_bg_rect_20_1c7dff"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:text="@string/privacy_policy_agree"
        android:includeFontPadding="false"
        android:textColor="@drawable/selector_btn_color_ffffff"
        android:textSize="@dimen/sp_26"
        app:layout_constraintEnd_toStartOf="@+id/bt_disagree"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_content2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/bt_disagree"
        android:layout_width="@dimen/dp_180"
        android:layout_height="@dimen/dp_72"
        android:layout_marginStart="@dimen/dp_40"
        android:background="@drawable/selector_btn_bg_rect_20_b3cdd5de_66909ead"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:text="@string/privacy_policy_disagree"
        android:includeFontPadding="false"
        android:textColor="@drawable/selector_btn_color_262e33_ffffff"
        android:textSize="28sp"
        app:layout_constraintBottom_toBottomOf="@+id/bt_agree"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/bt_agree"
        app:layout_constraintTop_toTopOf="@+id/bt_agree" />

</androidx.constraintlayout.widget.ConstraintLayout>