<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_80"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginLeft="@dimen/dp_48"
        android:layout_marginRight="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_10">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp_56"
            android:layout_height="@dimen/dp_56"
            android:scaleType="centerInside"
            android:src="@drawable/selector_icon_56_back"
            />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:gravity="center_vertical"
            android:text="@string/vip_payment_title"
            android:textSize="@dimen/sp_32"
            android:includeFontPadding="false"
            android:textColor="@color/normal_text_color"
            android:layout_weight="1"
            android:fontFamily="sans-serif-medium"
            android:paddingLeft="@dimen/dp_14"
            />

        <include layout="@layout/layout_app_logo"
            android:layout_height="match_parent"
            android:layout_width="wrap_content">
        </include>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_650"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/dp_32"
        android:layout_marginBottom="@dimen/dp_32"
        android:layout_marginLeft="@dimen/dp_64"
        android:layout_marginRight="@dimen/dp_64">


        <LinearLayout
            android:layout_width="@dimen/dp_808"
            android:layout_height="match_parent"
            android:paddingLeft="@dimen/dp_48"
            android:paddingRight="@dimen/dp_48"
            android:paddingTop="@dimen/dp_40"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:background="@drawable/bg_vip_pay"
            >
            
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp_40">

                <ImageView
                    android:id="@+id/iv_avatar"
                    android:layout_width="@dimen/dp_100"
                    android:layout_height="@dimen/dp_100"
                    android:src="@mipmap/ksongs_useravator_default">

                </ImageView>
                <TextView
                    android:id="@+id/tv_user_name"
                    android:layout_gravity="center_vertical"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_45"
                    android:paddingLeft="@dimen/dp_20"
                    android:gravity="center_vertical"
                    android:fontFamily="sans-serif-medium"
                    android:text="-"
                    android:textColor="@color/normal_text_color"
                    android:textSize="@dimen/sp_30">

                </TextView>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:background="@drawable/bg_vip_view" />

            <TextView
                android:id="@+id/tv_price"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_54"
                android:layout_marginTop="@dimen/dp_7"
                android:text="¥20"
                android:textSize="@dimen/sp_36"
                android:textColor="#FF00B2E3"
                android:textStyle="bold"
                />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="@dimen/dp_332"
                android:layout_height="@dimen/dp_332"
                android:layout_marginTop="@dimen/dp_6">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_qrcode"
                    android:layout_width="@dimen/dp_290"
                    android:layout_height="@dimen/dp_290"
                    android:background="@drawable/bg_qr"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent">
                    <com.tme.ktv.qrcode.QRCodeView
                        android:id="@+id/iv_payment_qr"
                        android:layout_width="@dimen/dp_240"
                        android:layout_height="@dimen/dp_240"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:id="@+id/layout_load_status"
                    android:layout_width="@dimen/dp_322"
                    android:layout_height="@dimen/dp_332"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:background="@mipmap/ksongs_qrcode_mark"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/iv_load_status"
                        android:layout_width="@dimen/dp_140"
                        android:layout_height="@dimen/dp_140">

                    </ImageView>

                    <TextView
                        android:id="@+id/tv_load_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        android:layout_marginTop="@dimen/dp_10">
                    </TextView>

                </LinearLayout>

                
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_42"
                android:text="微信扫描支付"
                android:textColor="@color/normal_text_color"
                android:textSize="@dimen/sp_28"
                />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/dp_32"
            android:layout_weight="1">
        </androidx.recyclerview.widget.RecyclerView>

    </LinearLayout>

</LinearLayout>