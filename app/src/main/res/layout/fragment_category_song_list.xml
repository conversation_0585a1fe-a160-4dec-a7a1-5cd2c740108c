<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:layout_marginStart="@dimen/dp_64"
        android:importantForAccessibility="no"
        android:src="@drawable/selector_icon_56_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"/>

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_100"
        android:layout_marginStart="@dimen/dp_14"
        tools:text="@string/user_feature_song_like"
        android:gravity="center_vertical"
        android:textColor="@color/title_color"
        android:fontFamily="sans-serif-medium"
        android:lineSpacingExtra="@dimen/dp_48"
        android:textSize="@dimen/sp_32"
        app:layout_constraintLeft_toRightOf="@id/iv_back"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_80"
        android:layout_marginEnd="@dimen/dp_190"
        android:layout_marginTop="@dimen/dp_10"
        android:importantForAccessibility="no"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@mipmap/logo_ksong" />

    <TextView
        android:id="@+id/tvLogoInfo"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_48"
        android:lineSpacingExtra="@dimen/dp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginEnd="@dimen/dp_64_5"
        android:layout_marginTop="@dimen/dp_24"
        android:text="@string/app_name"
        android:textSize="@dimen/sp_32"
        android:textColor="@color/right_logo_txt_color" />

    <!-- 独立的歌曲列表布局，不再依赖HotTopicListFragment -->
    <com.sgmw.common.widget.StateLayout
        android:id="@+id/stateLayout"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:loadingIvTopMargin="@dimen/dp_247"
        app:emptyIvTopMargin="@dimen/dp_166">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/srl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            style="@style/BaseRefreshStyle">

            <com.scwang.smart.refresh.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_124"/>

            <com.sgmw.common.widget.CustomRecyclerView
                android:id="@+id/rvCategorySongList"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                style="@style/recy_vertical_style"
                android:overScrollMode="never"
                android:background="@color/transparent"
                android:layout_marginHorizontal="@dimen/dp_64"
                android:layout_marginTop="@dimen/dp_24"/>

            <com.sgmw.ksongs.widget.CustomClassicsFooter
                android:layout_width="match_parent"
                app:srlDrawableProgress="@mipmap/icon48_load_more"
                app:srlDrawableProgressSize="@dimen/dp_48"
                app:srlDrawableMarginRight="@dimen/dp_8"
                app:srlTextSizeTitle="@dimen/sp_24"
                app:srlAccentColor="@color/settings_text_color"
                android:layout_height="@dimen/dp_124"/>

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </com.sgmw.common.widget.StateLayout>


</androidx.constraintlayout.widget.ConstraintLayout>