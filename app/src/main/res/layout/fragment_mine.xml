<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:layout_marginTop="@dimen/dp_8">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_592"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="@dimen/dp_620"
            android:layout_height="match_parent"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:background="@mipmap/bg_mine_user">

            <ImageView
                android:id="@+id/iv_avatar"
                android:layout_width="@dimen/dp_166"
                android:layout_height="@dimen/dp_166"
                android:layout_marginTop="@dimen/dp_88"
                android:src="@mipmap/icon166_default_avatar"/>

            <TextView
                android:id="@+id/tv_nick"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_45"
                android:textSize="@dimen/sp_30"
                android:textColor="@color/normal_text_color"
                android:layout_marginTop="@dimen/dp_18"
                android:maxWidth="@dimen/dp_424"
                android:fontFamily="sans-serif-medium"
                android:maxLines="1"
                android:ellipsize="end"/>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/dp_25">

                <ImageView
                    android:id="@+id/iv_vip"
                    android:layout_gravity="center_horizontal"
                    android:layout_width="@dimen/dp_64"
                    android:layout_height="@dimen/dp_32" />

                <TextView
                    android:id="@+id/tv_vip_peroid"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_42"
                    android:textSize="@dimen/sp_28"
                    android:layout_marginTop="@dimen/dp_17"
                    android:textColor="@color/white">

                </TextView>
            </LinearLayout>

            <Button
                android:id="@+id/btn_exit_account"
                android:layout_width="@dimen/dp_180"
                android:layout_height="@dimen/dp_72"
                android:text="@string/exit"
                android:includeFontPadding="false"
                android:fontFamily="sans-serif-medium"
                android:layout_marginTop="@dimen/dp_47"
                style="@style/WhiteButton" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_horizontal"
            android:layout_marginLeft="@dimen/dp_32"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_280"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/cl_history"
                    android:layout_width="@dimen/dp_554"
                    android:layout_height="match_parent"
                    android:background="@mipmap/bg_mine_history">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_60"
                        android:text="@string/history"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_40"
                        android:layout_gravity="center_vertical"
                        android:fontFamily="sans-serif-medium"
                        android:layout_marginLeft="@dimen/dp_60"
                        />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/cl_collect"
                    android:layout_width="@dimen/dp_554"
                    android:layout_height="match_parent"
                    android:background="@mipmap/bg_mine_collect"
                    android:layout_marginStart="@dimen/dp_32">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_60"
                        android:text="@string/collect"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_40"
                        android:fontFamily="sans-serif-medium"
                        android:layout_marginLeft="@dimen/dp_60"
                        android:layout_gravity="center_vertical" />

                </LinearLayout>


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_280"
                android:layout_marginTop="@dimen/dp_32"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/cl_setting"
                    android:layout_width="@dimen/dp_554"
                    android:layout_height="match_parent"
                    android:background="@mipmap/bg_mine_settings">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_60"
                        android:text="@string/settings"
                        android:fontFamily="sans-serif-medium"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_40"
                        android:layout_marginLeft="@dimen/dp_60"
                        android:layout_gravity="center_vertical" />

                </LinearLayout>
                <LinearLayout
                    android:id="@+id/iv_buy_vip"
                    android:layout_width="@dimen/dp_554"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp_32"
                    android:background="@mipmap/bg_mine_vip">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_60"
                        android:text="@string/vip_buy"
                        android:fontFamily="sans-serif-medium"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp_40"
                        android:layout_marginLeft="@dimen/dp_60"
                        android:layout_gravity="center_vertical" />

                </LinearLayout>

            </LinearLayout>


        </LinearLayout>


    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_42"
        android:gravity="center_horizontal|start"
        android:text="备案号：XXXXXXXXXX"
        android:textColor="@color/normal_text_color"
        android:textSize="@dimen/sp_28"
        android:layout_marginTop="@dimen/dp_16"/>

</LinearLayout>