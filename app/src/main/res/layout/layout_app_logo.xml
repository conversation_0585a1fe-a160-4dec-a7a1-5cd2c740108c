<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="horizontal"
    tools:ignore="UseCompoundDrawables">

    <ImageView
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_80"
        android:background="@mipmap/logo_ksong"
        android:importantForAccessibility="no"
        android:textSize="@dimen/sp_20" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:text="@string/app_name"
        android:textColor="@color/normal_text_color"
        android:textSize="@dimen/sp_32"/>

</LinearLayout>