<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/search_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusableInTouchMode="true">

    <LinearLayout
        android:id="@+id/layout_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_80"
        android:layout_marginStart="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_64_5"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/selector_icon_56_back"
            android:importantForAccessibility="no" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_search_box"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp_32"
            android:layout_weight="1"
            android:background="@drawable/bg_search_box"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_back"
            app:layout_constraintRight_toLeftOf="@id/btn_search"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_search_icon"
                android:layout_width="@dimen/dp_64"
                android:layout_height="@dimen/dp_64"
                android:layout_marginStart="@dimen/dp_12"
                android:background="@mipmap/icon64_search"
                android:importantForAccessibility="no"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/et_search_box"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@null"
                android:fontFamily="sans-serif-medium"
                android:hint="@string/search_default_hint"
                android:imeOptions="actionSearch"
                android:importantForAutofill="no"
                android:includeFontPadding="false"
                android:inputType="text"
                android:maxLines="1"
                android:textColor="@color/normal_text_color"
                android:textColorHint="@color/normal_text_color"
                android:textSize="@dimen/sp_28"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/iv_search_icon"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_search_box_clear"
                android:layout_width="@dimen/dp_64"
                android:layout_height="@dimen/dp_64"
                android:layout_marginEnd="@dimen/dp_12"
                android:background="@mipmap/icon_search_box_clear"
                android:importantForAccessibility="no"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/et_search_box"
                app:layout_constraintRight_toRightOf="@id/et_search_box"
                app:layout_constraintTop_toTopOf="@id/et_search_box" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <Button
            android:id="@+id/btn_search"
            style="@style/BlueButton"
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_32"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/title_search"
            android:textSize="@dimen/sp_26"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/layout_search_box"
            app:layout_constraintRight_toLeftOf="@id/layout_app_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <include
            android:id="@+id/layout_app_icon"
            layout="@layout/layout_app_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_32"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/btn_search"
            app:layout_constraintTop_toTopOf="parent" />

    </LinearLayout>

    <com.sgmw.common.widget.StateLayout
        android:id="@+id/sl_search_result"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_24"
        android:visibility="gone"
        app:emptyDataHintTxt="@string/search_empty_tips"
        app:emptyIvTopMargin="@dimen/dp_221"
        app:errorIvTopMargin="@dimen/dp_162"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_top"
        app:loadingIvTopMargin="@dimen/dp_243"
        app:showEmptyBtn="false">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabLayout"
                style="@style/SmallCustomTabLayout"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_60"
                android:layout_marginStart="@dimen/dp_24"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tabIndicatorAnimationDuration="160" />

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/viewPager"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/dp_64"
                android:layout_marginTop="@dimen/dp_24"
                android:layout_marginEnd="@dimen/dp_64"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tabLayout" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.sgmw.common.widget.StateLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout_suggestion_songs"
        style="@style/BaseRefreshStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_114"
        android:layout_marginEnd="@dimen/dp_48"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_top">

        <com.sgmw.common.widget.CustomRecyclerView
            android:id="@+id/recyclerview_suggestion_songs"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.sgmw.ksongs.widget.CustomClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_124"
            app:srlAccentColor="@color/settings_text_color"
            app:srlDrawableMarginRight="@dimen/dp_8"
            app:srlDrawableProgress="@mipmap/icon48_load_more"
            app:srlDrawableProgressSize="@dimen/dp_48"
            app:srlTextSizeTitle="@dimen/sp_24" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout_hot_search"
        style="@style/BaseRefreshStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_94"
        android:layout_marginRight="@dimen/dp_64"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/ll_hot_search"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.sgmw.common.widget.StateLayout
                android:id="@+id/sl_hot_search"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp_20"
                app:emptyDataHintTxt="@string/search_empty_tips"
                app:emptyIvTopMargin="@dimen/dp_100"
                app:errorIvTopMargin="@dimen/dp_100"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_hot_search"
                app:loadingIvTopMargin="@dimen/dp_180"
                app:showEmptyBtn="false">

                <com.sgmw.common.widget.CustomRecyclerView
                    android:id="@+id/recyclerview_hot_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </com.sgmw.common.widget.StateLayout>

        </LinearLayout>

        <com.sgmw.ksongs.widget.CustomClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_300"
            app:srlAccentColor="@color/settings_text_color"
            app:srlDrawableMarginRight="@dimen/dp_8"
            app:srlDrawableProgress="@mipmap/icon48_load_more"
            app:srlDrawableProgressSize="@dimen/dp_48"
            app:srlTextSizeTitle="@dimen/sp_24" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>