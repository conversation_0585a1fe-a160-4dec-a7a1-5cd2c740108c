<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/bg_shadow"
        android:background="@color/dialog_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_content"
        android:layout_width="@dimen/dp_1160"
        android:layout_height="@dimen/dp_660"
        android:layout_marginTop="@dimen/dp_165"
        android:background="@drawable/bg_play_list_dialog"
        android:layout_gravity="center_horizontal">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            style="@style/CustomTabLayout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_66"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_weight="1"
            app:layout_constraintTop_toTopOf="parent"
            app:tabGravity="start"
            app:tabIndicator="@drawable/tab_indicator"
            app:tabIndicatorAnimationDuration="160"
            app:tabIndicatorColor="@color/c_1990FF"
            app:tabIndicatorFullWidth="false"
            app:tabMode="scrollable"
            app:tabPaddingBottom="@dimen/dp_0"
            app:tabPaddingEnd="@dimen/dp_50"
            app:tabPaddingStart="@dimen/dp_50"
            app:tabRippleColor="@null"
            app:tabSelectedTextColor="@color/tab_text_color_selected"
            app:tabTextAppearance="@style/TabLayoutTextStyle"
            app:tabTextColor="@color/tab_text_color_normal"
            tools:ignore="RtlSymmetry" />

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:layout_marginStart="@dimen/dp_20"
            android:importantForAccessibility="no"
            android:src="@mipmap/icon64_close"
            app:layout_constraintBottom_toBottomOf="@+id/tabLayout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tabLayout" />

        <TextView
            android:id="@+id/tv_clear_list"
            android:layout_width="@dimen/dp_136"
            android:layout_height="@dimen/dp_52"
            android:layout_marginEnd="30dp"
            android:background="@drawable/selector_btn_bg_rect_12_b3cdd5de_66909ead"
            android:drawableStart="@mipmap/icon64_del"
            android:fontFamily="sans-serif-medium"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:paddingVertical="@dimen/dp_2"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_24"
            android:text="@string/demand_clear"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_24"
            app:layout_constraintBottom_toBottomOf="@+id/tabLayout"
            app:layout_constraintEnd_toStartOf="@+id/tv_out_of_order"
            app:layout_constraintTop_toTopOf="@+id/tabLayout" />

        <TextView
            android:id="@+id/tv_out_of_order"
            android:layout_width="@dimen/dp_136"
            android:layout_height="@dimen/dp_52"
            android:layout_marginEnd="30dp"
            android:background="@drawable/selector_btn_bg_rect_12_b3cdd5de_66909ead"
            android:drawableStart="@mipmap/icon64_random"
            android:fontFamily="sans-serif-medium"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:paddingVertical="@dimen/dp_2"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_24"
            android:text="@string/demand_out_off_order"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_24"
            app:layout_constraintBottom_toBottomOf="@+id/tabLayout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tabLayout" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tabLayout" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>