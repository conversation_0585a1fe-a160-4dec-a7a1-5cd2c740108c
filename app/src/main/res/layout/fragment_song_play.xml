<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true">

    <!--加载中-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_load"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_song_load"
        android:visibility="gone"
        tools:visibility="gone">

        <ImageView
            android:id="@+id/iv_load_back"
            android:layout_width="@dimen/dp_56"
            android:layout_height="@dimen/dp_56"
            android:layout_marginStart="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_24"
            android:importantForAccessibility="no"
            android:src="@mipmap/icon_load_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_64_5"
            android:text="@string/app_name"
            android:textColor="@color/right_logo_txt_color"
            android:textSize="@dimen/sp_32"
            app:layout_constraintBottom_toBottomOf="@+id/iv_logo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_logo" />

        <ImageView
            android:id="@+id/iv_logo"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_80"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_14"
            android:importantForAccessibility="no"
            android:src="@mipmap/logo_ksong"
            app:layout_constraintEnd_toStartOf="@+id/tv_logo"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_load_song_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_34"
            android:fontFamily="sans-serif-medium"
            android:textColor="@color/text_song_load_name"
            android:textSize="@dimen/sp_32"
            app:layout_constraintBottom_toTopOf="@+id/iv_load_song"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="七里香" />

        <ImageView
            android:id="@+id/iv_load_song"
            android:layout_width="@dimen/dp_386"
            android:layout_height="@dimen/dp_386"
            android:layout_marginBottom="@dimen/dp_10"
            android:importantForAccessibility="no"
            android:scaleType="centerCrop"
            android:src="@mipmap/icon_music_default_bg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/pb_song"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="@dimen/dp_386"
            android:layout_height="@dimen/dp_4"
            android:layout_marginTop="@dimen/dp_42"
            android:progressDrawable="@drawable/song_play_progress"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_load_song"
            tools:progress="30" />

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_40"
            android:textColor="@color/text_song_load_progress"
            android:textSize="@dimen/sp_28"
            app:layout_constraintEnd_toEndOf="@+id/pb_song"
            app:layout_constraintStart_toStartOf="@+id/pb_song"
            app:layout_constraintTop_toBottomOf="@+id/pb_song"
            tools:text="已加载40%" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--加载错误-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_load_err"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_main_color"
        android:visibility="gone"
        tools:visibility="gone">

        <ImageView
            android:id="@+id/iv_err_back"
            android:layout_width="@dimen/dp_56"
            android:layout_height="@dimen/dp_56"
            android:layout_marginStart="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_24"
            android:importantForAccessibility="no"
            android:src="@mipmap/icon_load_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_err_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_64_5"
            android:text="@string/app_name"
            android:textColor="@color/right_logo_txt_color"
            android:textSize="@dimen/sp_32"
            app:layout_constraintBottom_toBottomOf="@+id/iv_err_logo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_err_logo" />

        <ImageView
            android:id="@+id/iv_err_logo"
            android:layout_width="@dimen/dp_80"
            android:layout_height="@dimen/dp_80"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_14"
            android:importantForAccessibility="no"
            android:src="@mipmap/logo_ksong"
            app:layout_constraintEnd_toStartOf="@+id/tv_err_logo"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_err"
            android:layout_width="@dimen/dp_360"
            android:layout_height="@dimen/dp_160"
            android:layout_marginTop="@dimen/dp_176"
            android:importantForAccessibility="no"
            android:scaleType="centerCrop"
            android:src="@mipmap/icon_song_play_no_net"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_err_logo" />

        <TextView
            android:id="@+id/tv_err"
            android:layout_width="@dimen/dp_327"
            android:layout_height="@dimen/dp_84"
            android:layout_marginTop="@dimen/dp_6"
            android:gravity="center"
            android:text="@string/play_error_no_net"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_28"
            app:barrierAllowsGoneWidgets="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_err" />

        <TextView
            android:id="@+id/tv_retry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_46"
            android:background="@drawable/bg_retry_button"
            android:includeFontPadding="false"
            android:paddingHorizontal="@dimen/dp_64"
            android:paddingTop="@dimen/dp_15"
            android:paddingBottom="@dimen/dp_18"
            android:text="@string/play_error_retry"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_26"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_err" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--正常播放-->
    <com.sgmw.ksongs.widget.VideoGestureConstraintLayout
        android:id="@+id/cl_singing"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible">

        <!--播放组件-->
        <ksong.support.video.ktv.KtvVideoLayout
            android:id="@+id/video_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <!--播放默认图-->
        <ImageView
            android:id="@+id/iv_show_pic_url"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:importantForAccessibility="no"
            android:src="@mipmap/bg_song_play_defalut"
            android:visibility="gone" />

        <!--播放信息-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/dp_250"
            android:layout_height="@dimen/dp_90"
            android:layout_marginStart="@dimen/dp_144"
            android:layout_marginTop="@dimen/dp_64"
            android:background="@drawable/bg_video_next_song"
            android:orientation="vertical"
            android:paddingEnd="@dimen/dp_14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="RtlSymmetry">

            <ImageView
                android:id="@+id/iv_singing_song"
                android:layout_width="@dimen/dp_90"
                android:layout_height="match_parent"
                android:importantForAccessibility="no"
                android:src="@mipmap/icon_music_default_bg"
                app:layout_constraintStart_toStartOf="parent"
                tools:src="@mipmap/icon_music_default_bg" />

            <TextView
                android:id="@+id/tv_singing_song_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_16"
                android:ellipsize="end"
                android:fontFamily="sans-serif-medium"
                android:maxWidth="@dimen/dp_120"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_20"
                app:layout_constraintBottom_toTopOf="@+id/ll_next_song"
                app:layout_constraintStart_toEndOf="@+id/iv_singing_song"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.5"
                app:layout_constraintVertical_chainStyle="packed"
                tools:text="七里香" />

            <LinearLayout
                android:id="@+id/ll_next_song"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tv_singing_song_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_singing_song_name">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="sans-serif-medium"
                    android:text="@string/play_next_song"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_20" />

                <TextView
                    android:id="@+id/tv_next_song_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_10"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif-medium"
                    android:maxWidth="@dimen/dp_60"
                    android:maxLines="1"
                    android:textColor="@color/color_B3FFFFFF"
                    android:textSize="@dimen/sp_20"
                    tools:text="七里香" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--歌词组件-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/fl_lyric"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible">

            <!--倒计时-->
            <com.sgmw.ksongs.widget.LyricDotView
                android:id="@+id/lyric_dot_view"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_45"
                android:layout_marginStart="@dimen/dp_200"
                android:layout_marginEnd="@dimen/dp_36"
                android:layout_marginBottom="@dimen/dp_24"
                android:visibility="invisible"
                app:layout_constraintBottom_toTopOf="@+id/lyric_view"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="visible" />

            <com.tme.ktv.lyric.widget.LyricView
                android:id="@+id/lyric_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal|bottom"
                android:layout_marginStart="@dimen/dp_200"
                android:layout_marginEnd="@dimen/dp_200"
                android:background="@android:color/transparent"
                app:layoutMode="ktv"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:lightTextColor="@color/btn_1C7DFF"
                app:lightTextFont="@font/fangzheng_font"
                app:lightTextSize="@dimen/sp_60"
                app:lightUnselectTextColor="@android:color/white"
                app:lightUnselectTextFont="@font/fangzheng_font"
                app:lightUnselectTextStrokeColor="@android:color/black"
                app:lightUnselectTextStrokeWidth="5"
                app:lineMargin="@dimen/dp_10"
                app:lineNumber="9"
                app:lyricBackgroundColor="#00000000"
                app:shadowColor="#00000000"
                app:shadowEnable="false"
                app:strokeColor="@android:color/white"
                app:strokeW="5"
                app:textColor="@color/white"
                app:textFont="@font/fangzheng_font"
                app:textSize="@dimen/sp_60"
                app:textStrokeColor="@android:color/black"
                app:textStrokeWidth="5" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--音准器组件-->
        <FrameLayout
            android:id="@+id/fl_score"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:visibility="invisible">

            <com.tme.ktv.intonation.view.PerfectAnimView
                android:id="@+id/karaoke_perfect_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <com.tme.ktv.intonation.view.SimpleScoreBar
                android:id="@+id/pk_score_bar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_45"
                android:layout_marginStart="@dimen/pk_score_layout_margin_left"
                android:layout_marginTop="@dimen/dp_160"
                android:focusable="false"
                android:visibility="invisible" />

            <com.tme.ktv.intonation.SingIntonationViewer
                android:id="@+id/player_float_intonation_viewer"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_161"
                android:layout_gravity="bottom"
                android:background="@color/bg_score" />

        </FrameLayout>

        <!--设置页面-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_setting"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/iv_setting_back"
                android:layout_width="@dimen/dp_56"
                android:layout_height="@dimen/dp_56"
                android:layout_marginStart="@dimen/dp_64"
                android:layout_marginTop="@dimen/dp_64"
                android:background="@drawable/bg_setting_close"
                android:importantForAccessibility="no"
                android:src="@mipmap/icon_setting_close"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.sgmw.ksongs.widget.PlaySettingItemView
                android:id="@+id/item_vocals_switch"
                android:layout_width="@dimen/dp_140"
                android:layout_height="@dimen/dp_140"
                android:layout_marginStart="@dimen/dp_440"
                android:layout_marginTop="@dimen/dp_220"
                app:itemIcon="@mipmap/icon_48_vocal_accompaniment"
                app:itemText="@string/play_vocals_open"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.sgmw.ksongs.widget.PlaySettingItemView
                android:id="@+id/item_mv_switch"
                android:layout_width="@dimen/dp_140"
                android:layout_height="@dimen/dp_140"
                android:layout_marginStart="@dimen/dp_40"
                android:layout_marginTop="@dimen/dp_220"
                app:itemIcon="@mipmap/icon_48_mv"
                app:itemText="@string/play_mv_open"
                app:layout_constraintStart_toEndOf="@+id/item_vocals_switch"
                app:layout_constraintTop_toTopOf="parent" />

            <com.sgmw.ksongs.widget.PlaySettingItemView
                android:id="@+id/item_score_switch"
                android:layout_width="@dimen/dp_140"
                android:layout_height="@dimen/dp_140"
                android:layout_marginStart="@dimen/dp_40"
                app:itemIcon="@mipmap/icon_48_recommend"
                app:itemText="@string/play_score_close"
                app:layout_constraintBottom_toBottomOf="@+id/item_mv_switch"
                app:layout_constraintStart_toEndOf="@+id/item_mv_switch"
                app:layout_constraintTop_toTopOf="@+id/item_mv_switch" />

            <com.sgmw.ksongs.widget.PlaySettingItemView
                android:id="@+id/item_tuning_switch"
                android:layout_width="@dimen/dp_140"
                android:layout_height="@dimen/dp_140"
                android:layout_marginStart="@dimen/dp_40"
                app:iconHeight="@dimen/dp_64"
                app:iconMarginTop="@dimen/dp_14"
                app:iconWidth="@dimen/dp_64"
                app:itemIcon="@mipmap/icon_64_voic_set"
                app:itemText="@string/play_tuning"
                app:layout_constraintBottom_toBottomOf="@+id/item_score_switch"
                app:layout_constraintStart_toEndOf="@+id/item_score_switch"
                app:layout_constraintTop_toTopOf="@+id/item_score_switch" />

            <com.sgmw.ksongs.widget.PlaySettingItemView
                android:id="@+id/item_pitcher_switch"
                android:layout_width="@dimen/dp_140"
                android:layout_height="@dimen/dp_140"
                android:layout_marginStart="@dimen/dp_40"
                app:itemIcon="@mipmap/icon_48_music"
                app:itemText="@string/play_pitcher_open"
                app:layout_constraintBottom_toBottomOf="@+id/item_tuning_switch"
                app:layout_constraintStart_toEndOf="@+id/item_tuning_switch"
                app:layout_constraintTop_toTopOf="@+id/item_tuning_switch" />

            <com.sgmw.ksongs.widget.PlaySettingItemView
                android:id="@+id/item_mic_switch"
                android:layout_width="@dimen/dp_140"
                android:layout_height="@dimen/dp_140"
                android:layout_marginStart="@dimen/dp_40"
                app:itemIcon="@mipmap/icon_48_microphone_close"
                app:itemText="@string/play_mic_disconnect"
                app:layout_constraintBottom_toBottomOf="@+id/item_pitcher_switch"
                app:layout_constraintStart_toEndOf="@+id/item_pitcher_switch"
                app:layout_constraintTop_toTopOf="@+id/item_pitcher_switch" />

            <ImageView
                android:id="@+id/iv_collect"
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_100"
                android:layout_marginStart="@dimen/dp_50"
                android:layout_marginTop="@dimen/dp_40"
                android:background="@drawable/bg_song_play_setting_round"
                android:importantForAccessibility="no"
                android:padding="@dimen/dp_6"
                android:src="@mipmap/icon72_collect"
                app:layout_constraintStart_toEndOf="@+id/item_vocals_switch"
                app:layout_constraintTop_toBottomOf="@+id/item_vocals_switch" />

            <ImageView
                android:id="@+id/iv_replay"
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_100"
                android:layout_marginStart="@dimen/dp_40"
                android:background="@drawable/bg_song_play_setting_round"
                android:importantForAccessibility="no"
                android:padding="@dimen/dp_14"
                android:src="@mipmap/icon_72_prompt"
                app:layout_constraintBottom_toBottomOf="@+id/iv_collect"
                app:layout_constraintStart_toEndOf="@+id/iv_collect"
                app:layout_constraintTop_toTopOf="@+id/iv_collect" />

            <ImageView
                android:id="@+id/iv_play_status"
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_100"
                android:layout_marginStart="@dimen/dp_40"
                android:background="@drawable/bg_song_play_setting_round"
                android:importantForAccessibility="no"
                android:padding="@dimen/dp_14"
                android:src="@mipmap/icon_72_pause"
                app:layout_constraintBottom_toBottomOf="@+id/iv_replay"
                app:layout_constraintStart_toEndOf="@+id/iv_replay"
                app:layout_constraintTop_toTopOf="@+id/iv_replay"
                tools:src="@mipmap/icon_72_play" />

            <ImageView
                android:id="@+id/iv_play_next"
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_100"
                android:layout_marginStart="@dimen/dp_40"
                android:background="@drawable/bg_song_play_setting_round"
                android:importantForAccessibility="no"
                android:padding="@dimen/dp_12_67"
                android:src="@mipmap/icon_75_next_song"
                app:layout_constraintBottom_toBottomOf="@+id/iv_replay"
                app:layout_constraintStart_toEndOf="@+id/iv_play_status"
                app:layout_constraintTop_toTopOf="@+id/iv_replay" />

            <FrameLayout
                android:id="@+id/fl_play_list"
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_100"
                android:layout_marginStart="@dimen/dp_40"
                android:background="@drawable/bg_song_play_setting_round"
                android:src="@mipmap/icon_play_next"
                app:layout_constraintBottom_toBottomOf="@+id/iv_replay"
                app:layout_constraintStart_toEndOf="@+id/iv_play_next"
                app:layout_constraintTop_toTopOf="@+id/iv_replay">

                <ImageView
                    android:id="@+id/iv_play_list_icon"
                    android:layout_width="@dimen/dp_72"
                    android:layout_height="@dimen/dp_72"
                    android:layout_gravity="center"
                    android:importantForAccessibility="no"
                    android:src="@mipmap/icon_72_musiclist" />

                <TextView
                    android:id="@+id/tv_play_list_count"
                    android:layout_width="@dimen/dp_32"
                    android:layout_height="@dimen/dp_32"
                    android:layout_gravity="end"
                    android:layout_marginTop="@dimen/dp_16"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:background="@drawable/bg_song_play_list_count"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_12"
                    tools:text="99+" />

            </FrameLayout>

            <!--播放进度条-->
            <androidx.appcompat.widget.AppCompatSeekBar
                android:id="@+id/seek_bar_progress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_164"
                android:layout_marginEnd="@dimen/dp_164"
                android:layout_marginBottom="@dimen/dp_221"
                android:maxHeight="@dimen/dp_8"
                android:minHeight="@dimen/dp_8"
                android:progressDrawable="@drawable/progress_song_play"
                android:soundEffectsEnabled="false"
                android:splitTrack="false"
                android:thumb="@drawable/thumb_song_play"
                android:thumbOffset="0dp"
                app:layout_constraintBottom_toBottomOf="parent" />

            <TextView
                android:id="@+id/tv_play_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_180"
                android:layout_marginTop="@dimen/dp_20"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_20"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/seek_bar_progress"
                tools:text="00:30" />

            <TextView
                android:id="@+id/tv_end_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_180"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_20"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/seek_bar_progress"
                tools:text="05:30" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--麦克风权限-->
        <TextView
            android:id="@+id/tv_mic_permission"
            android:layout_width="@dimen/dp_473"
            android:layout_height="@dimen/dp_56"
            android:layout_marginStart="@dimen/dp_64"
            android:layout_marginTop="@dimen/dp_374"
            android:background="@drawable/bg_skip_prelude"
            android:gravity="center"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text="@string/open_mic_permission"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_26"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <!--跳过前奏-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_skip_prelude"
            android:layout_width="@dimen/dp_454"
            android:layout_height="@dimen/dp_56"
            android:layout_marginStart="@dimen/dp_64"
            android:layout_marginTop="@dimen/dp_450"
            android:background="@drawable/bg_skip_prelude"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tv_skip_prelude"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_24"
                android:includeFontPadding="false"
                android:text="@string/skip_prelude"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_26"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_skip_prelude_countdown"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/skip_prelude_countdown"
                android:textColor="@color/color_FF1C7DFF"
                android:textSize="@dimen/sp_26"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tv_skip_prelude"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_skip_prelude_close"
                android:layout_width="@dimen/dp_64"
                android:layout_height="@dimen/dp_64"
                android:importantForAccessibility="no"
                android:src="@mipmap/icon_skip_prelude_close"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--左侧屏幕亮度设置-->
        <com.sgmw.ksongs.widget.BrightnessView
            android:id="@+id/bright_setting"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_480"
            android:layout_marginStart="@dimen/dp_64"
            android:layout_marginTop="@dimen/dp_220"
            android:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <!--右侧声音设置-->
        <com.sgmw.ksongs.widget.VolumeView
            android:id="@+id/volume_setting"
            android:layout_width="@dimen/dp_72"
            android:layout_height="@dimen/dp_480"
            android:layout_marginTop="@dimen/dp_220"
            android:layout_marginEnd="@dimen/dp_64"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </com.sgmw.ksongs.widget.VideoGestureConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>