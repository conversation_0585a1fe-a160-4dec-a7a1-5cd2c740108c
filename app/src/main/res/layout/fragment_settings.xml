<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_100"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_48"
        android:paddingEnd="@dimen/dp_64_5">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp_56"
            android:layout_height="@dimen/dp_56"
            android:background="@drawable/selector_icon_56_back"
            android:importantForAccessibility="no" />

        <TextView
            style="@style/TitleStyle"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:includeFontPadding="false"
            android:text="@string/settings" />

        <include
            layout="@layout/layout_app_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_96"
        android:layout_marginLeft="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_32"
        android:layout_marginRight="@dimen/dp_64"
        android:background="@drawable/bg_item_settings"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_32"
        android:paddingEnd="@dimen/dp_40">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/settings_definition"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_26"
            tools:ignore="InefficientWeight" />

        <com.sgmw.ksongs.widget.NewsRadioGroup
            android:id="@+id/rp_definition"
            android:layout_width="@dimen/dp_660"
            android:layout_height="@dimen/dp_72"
            android:layout_gravity="center"
            android:background="@drawable/bg_radiogroup"
            android:gravity="center"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/rb_standard"
                style="@style/CustomRadioButton"
                android:layout_width="@dimen/dp_212"
                android:layout_height="@dimen/dp_64"
                android:checked="false"
                android:includeFontPadding="false"
                android:text="@string/standard_definition" />

            <RadioButton
                android:id="@+id/rb_high"
                style="@style/CustomRadioButton"
                android:layout_width="@dimen/dp_212"
                android:layout_height="@dimen/dp_64"
                android:layout_marginLeft="@dimen/dp_8"
                android:layout_marginRight="@dimen/dp_8"
                android:checked="true"
                android:includeFontPadding="false"
                android:text="@string/high_definition" />

            <RadioButton
                android:id="@+id/rb_super"
                style="@style/CustomRadioButton"
                android:layout_width="@dimen/dp_212"
                android:layout_height="@dimen/dp_64"
                android:checked="false"
                android:drawableEnd="@mipmap/setting_vip"
                android:drawablePadding="-60dp"
                android:includeFontPadding="false"
                android:paddingEnd="@dimen/dp_20"
                android:text="@string/super_definition"
                tools:ignore="RtlSymmetry" />

        </com.sgmw.ksongs.widget.NewsRadioGroup>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_96"
        android:layout_marginLeft="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_32"
        android:layout_marginRight="@dimen/dp_64"
        android:background="@drawable/bg_item_settings"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_32"
        android:paddingEnd="@dimen/dp_40">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/settings_soft_version"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_26"
            tools:ignore="InefficientWeight" />

        <TextView
            android:id="@+id/tv_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/settings_text_color"
            android:textSize="@dimen/sp_28" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_96"
        android:layout_marginLeft="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_32"
        android:layout_marginRight="@dimen/dp_64"
        android:background="@drawable/bg_item_settings"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_32"
        android:paddingEnd="@dimen/dp_40">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/settings_clear_cache"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_26"
            tools:ignore="InefficientWeight" />

        <TextView
            android:id="@+id/tv_cache"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="25M"
            android:textColor="@color/settings_text_color"
            android:textSize="@dimen/sp_28"
            tools:ignore="HardcodedText" />

        <Button
            android:id="@+id/btn_clear_cache"
            style="@style/GrayButton"
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_24"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/settings_clear" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_mic"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_96"
        android:layout_marginLeft="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_32"
        android:layout_marginRight="@dimen/dp_64"
        android:background="@drawable/bg_item_settings"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/dp_32"
        android:paddingRight="@dimen/dp_32"
        android:visibility="gone">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/settings_mic_connecttion_guide"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_26"
            tools:ignore="InefficientWeight" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@mipmap/icon64_details"
            android:importantForAccessibility="no" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_privacy_service"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_96"
        android:layout_marginLeft="@dimen/dp_64"
        android:layout_marginTop="@dimen/dp_32"
        android:layout_marginRight="@dimen/dp_64"
        android:background="@drawable/bg_item_settings"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/dp_32"
        android:paddingRight="@dimen/dp_32">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/settings_private_service"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_26"
            tools:ignore="InefficientWeight" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@mipmap/icon64_details"
            android:importantForAccessibility="no" />

    </LinearLayout>

</LinearLayout>