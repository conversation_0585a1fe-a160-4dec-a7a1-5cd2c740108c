<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:layout_marginStart="@dimen/dp_64"
        android:src="@drawable/selector_icon_56_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"/>


    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_100"
        android:layout_marginStart="@dimen/dp_14"
        android:text="@string/collect"
        android:fontFamily="sans-serif-medium"
        android:lineSpacingExtra="@dimen/dp_48"
        android:gravity="center_vertical"
        android:textColor="@color/title_color"
        android:textSize="@dimen/sp_32"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toRightOf="@id/iv_back"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tvSelectAll"
        android:layout_width="@dimen/dp_184"
        android:layout_height="@dimen/dp_52"
        android:fontFamily="sans-serif-medium"
        app:layout_constraintRight_toLeftOf="@+id/tvDelete"
        app:layout_constraintTop_toTopOf="@+id/tvTitle"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        android:layout_marginEnd="@dimen/dp_32"
        android:text="@string/select_all"
        android:textColor="@drawable/selector_btn_color_ffffff"
        android:textSize="@dimen/sp_24"
        android:includeFontPadding="false"
        android:gravity="center"
        android:background="@drawable/selector_btn_bg_rect_12_1c7dff" />


    <TextView
        android:id="@+id/tvDelete"
        android:layout_width="@dimen/dp_184"
        android:layout_height="@dimen/dp_52"
        android:fontFamily="sans-serif-medium"
        app:layout_constraintRight_toLeftOf="@+id/tvCancel"
        app:layout_constraintTop_toTopOf="@+id/tvTitle"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        android:layout_marginEnd="@dimen/dp_32"
        android:text="@string/delete"
        android:includeFontPadding="false"
        android:textColor="@drawable/selector_btn_color_ffffff"
        android:textSize="@dimen/sp_24"
        android:gravity="center"
        android:background="@drawable/selector_btn_bg_rect_12_f76666" />


    <TextView
        android:id="@+id/tvCancel"
        android:layout_width="@dimen/dp_184"
        android:layout_height="@dimen/dp_52"
        android:fontFamily="sans-serif-medium"
        app:layout_constraintRight_toLeftOf="@+id/ivLogo"
        app:layout_constraintTop_toTopOf="@+id/tvTitle"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        android:layout_marginEnd="@dimen/dp_32"
        android:text="@string/cancel"
        android:textColor="@drawable/selector_btn_color_262e33_ffffff"
        android:textSize="@dimen/sp_24"
        android:gravity="center"
        android:includeFontPadding="false"
        android:background="@drawable/selector_btn_bg_rect_12_b3cdd5de_66909ead" />

    <TextView
        android:id="@+id/tvEdit"
        android:visibility="gone"
        android:layout_width="@dimen/dp_184"
        android:layout_height="@dimen/dp_52"
        android:fontFamily="sans-serif-medium"
        app:layout_constraintRight_toLeftOf="@+id/ivLogo"
        app:layout_constraintTop_toTopOf="@+id/tvTitle"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        android:layout_marginEnd="@dimen/dp_32"
        android:text="@string/edit"
        android:textColor="@drawable/selector_btn_color_262e33_ffffff"
        android:textSize="@dimen/sp_24"
        android:includeFontPadding="false"
        android:gravity="center"
        android:background="@drawable/selector_btn_bg_rect_12_b3cdd5de_66909ead" />


    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_80"
        android:layout_marginEnd="@dimen/dp_190"
        android:layout_marginTop="@dimen/dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@mipmap/logo_ksong" />

    <TextView
        android:id="@+id/tvLogoInfo"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginEnd="@dimen/dp_64_5"
        android:layout_marginTop="@dimen/dp_24"
        android:text="@string/app_name"
        android:textSize="@dimen/sp_32"
        android:textColor="@color/right_logo_txt_color" />

    <com.sgmw.common.widget.StateLayout
        android:id="@+id/stateLayout"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:loadingIvTopMargin="@dimen/dp_257"
        app:emptyIvTopMargin="@dimen/dp_176"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle">

        <com.sgmw.common.widget.CustomRecyclerView
            android:id="@+id/rvCollect"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            style="@style/recy_vertical_style"
            android:overScrollMode="never"
            android:background="@color/transparent"
            android:layout_marginHorizontal="@dimen/dp_64"
            android:layout_marginTop="@dimen/dp_24" />

    </com.sgmw.common.widget.StateLayout>



</androidx.constraintlayout.widget.ConstraintLayout>