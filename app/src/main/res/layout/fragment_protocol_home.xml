<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_height="@dimen/dp_80"
        android:layout_width="wrap_content"
        app:tabIndicatorAnimationDuration="160"
        app:layout_constraintLeft_toLeftOf="@+id/iv_back"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginLeft="@dimen/dp_8"
        style="@style/BigCustomTabLayout">

    </com.google.android.material.tabs.TabLayout>

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_56"
        android:layout_height="@dimen/dp_56"
        android:src="@drawable/selector_icon_56_back"
        android:layout_marginTop="@dimen/dp_22"
        android:layout_marginLeft="@dimen/dp_64"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>


    <include
        layout="@layout/layout_app_logo"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_80"
        android:layout_marginRight="@dimen/dp_64_5"
        android:layout_marginTop="@dimen/dp_10"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_back"
        app:layout_constraintBottom_toBottomOf="@+id/iv_back">

    </include>


    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:background="@color/mic_connection_guide_right_bg_color"
        android:layout_marginTop="@dimen/dp_24"
        android:layout_marginLeft="@dimen/dp_64"
        android:layout_marginRight="@dimen/dp_64"
        android:layout_marginBottom="@dimen/dp_64"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout"
        app:layout_constraintBottom_toBottomOf="parent"/>


</androidx.constraintlayout.widget.ConstraintLayout>