<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_main_color"
    android:orientation="vertical">

    <View
        android:id="@+id/topBarView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:layout_constraintBottom_toTopOf="@+id/ll_play_control"
        app:layout_constraintTop_toBottomOf="@+id/topBarView">

        <fragment
            android:id="@+id/nav_host_fragment_activity_main"
            android:name="com.sgmw.ksongs.widget.CustomNavHostFragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:defaultNavHost="true"
            app:navGraph="@navigation/navigation" />

    </FrameLayout>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ll_play_control"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_96"
        android:background="@color/bg_play_control"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@+id/bottomBarView">

        <ImageView
            android:id="@+id/iv_main_play"
            android:layout_width="@dimen/dp_96"
            android:layout_height="@dimen/dp_96"
            android:layout_marginStart="@dimen/dp_20"
            android:importantForAccessibility="no"
            android:src="@drawable/selector_play" />

        <ImageView
            android:id="@+id/iv_next"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:layout_marginStart="@dimen/dp_4"
            android:importantForAccessibility="no"
            android:src="@drawable/selector_play_next" />

        <TextView
            android:id="@+id/tv_lyric"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/main_lyric_default"
            android:textColor="@color/font_play_control_lyrics"
            android:textSize="@dimen/sp_28" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/rl_list"
            android:layout_width="wrap_content"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/iv_list"
                android:layout_width="@dimen/dp_64"
                android:layout_height="@dimen/dp_64"
                android:layout_marginEnd="@dimen/dp_36"
                android:importantForAccessibility="no"
                android:src="@drawable/icon_play_control_list"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_demand_count"
                android:layout_width="@dimen/dp_32"
                android:layout_height="@dimen/dp_32"
                android:layout_gravity="end"
                android:layout_marginTop="@dimen/dp_8"
                android:layout_marginEnd="@dimen/dp_22"
                android:background="@drawable/bg_song_play_list_count"
                android:gravity="center"
                android:lineSpacingExtra="@dimen/dp_18"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="99+" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.LinearLayoutCompat>

    <View
        android:id="@+id/bottomBarView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_90"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>