<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/bg_shadow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/dialog_mask" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_content"
        android:layout_width="@dimen/dp_804"
        android:layout_height="@dimen/dp_450"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_270"
        android:background="@drawable/bg_vehicle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_64"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:background="@mipmap/icon64_close"
            android:importantForAccessibility="no"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_48"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/vehicle_video_dialog_title"
            android:textColor="@color/normal_text_color"
            android:textSize="@dimen/sp_32"
            app:layout_constraintBottom_toBottomOf="@+id/iv_back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_back" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="@dimen/dp_692"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_56"
            android:layout_marginTop="@dimen/dp_70"
            android:gravity="center"
            android:text="@string/vehicle_video_dialog_content"
            android:textColor="@color/font_main_color"
            android:textSize="@dimen/sp_28"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <TextView
            android:id="@+id/btn_confirm"
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_72"
            android:layout_marginBottom="@dimen/dp_32"
            android:background="@drawable/selector_btn_bg_rect_20_1c7dff"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/vehicle_video_dialog_confirm"
            android:textColor="@drawable/selector_btn_color_ffffff"
            android:textSize="@dimen/sp_26"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/btn_go_setting"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/btn_go_setting"
            android:layout_width="@dimen/dp_180"
            android:layout_height="@dimen/dp_72"
            android:layout_marginStart="@dimen/dp_36"
            android:background="@drawable/selector_btn_bg_rect_20_b3cdd5de_66909ead"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/vehicle_video_dialog_setting"
            android:textColor="@drawable/selector_btn_color_262e33_ffffff"
            android:textSize="@dimen/sp_26"
            app:layout_constraintBottom_toBottomOf="@+id/btn_confirm"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/btn_confirm"
            app:layout_constraintTop_toTopOf="@+id/btn_confirm" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>