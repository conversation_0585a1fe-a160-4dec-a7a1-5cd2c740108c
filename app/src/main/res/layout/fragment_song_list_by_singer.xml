<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_100"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_48"
        android:paddingEnd="@dimen/dp_64_5"
        >
        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/dp_56"
            android:layout_height="@dimen/dp_56"
            android:background="@drawable/selector_icon_56_back"
            android:importantForAccessibility="no" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            style="@style/TitleStyle"
            android:fontFamily="sans-serif-medium"/>

        <include
            layout="@layout/layout_app_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

        </include>

    </LinearLayout>

    <com.sgmw.common.widget.StateLayout
        android:id="@+id/stateLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:errorIvTopMargin="@dimen/dp_176"
        app:loadingIvTopMargin="@dimen/dp_257"
        app:emptyIvTopMargin="@dimen/dp_255"
        app:emptyDataHintTxt="@string/empty_tips"
        app:showEmptyBtn="false">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp_24"
            android:layout_marginStart="@dimen/dp_64"
            android:layout_marginEnd="@dimen/dp_64"
            style="@style/BaseRefreshStyle">

            <com.sgmw.common.widget.CustomRecyclerView
                android:id="@+id/recyclerview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>

            <com.sgmw.ksongs.widget.CustomClassicsFooter
                android:layout_width="match_parent"
                app:srlDrawableProgress="@mipmap/icon48_load_more"
                app:srlDrawableProgressSize="@dimen/dp_48"
                app:srlDrawableMarginRight="@dimen/dp_8"
                app:srlTextSizeTitle="@dimen/sp_24"
                app:srlAccentColor="@color/settings_text_color"
                android:layout_height="@dimen/dp_124"/>

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </com.sgmw.common.widget.StateLayout>




</LinearLayout>